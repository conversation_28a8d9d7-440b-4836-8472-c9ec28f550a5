# Import required libraries
import json
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
import seaborn as sns
import pandas as pd
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM
from IPython.display import display, HTML
import ipywidgets as widgets
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import warnings
warnings.filterwarnings('ignore')

# Set up plotting style
plt.style.use('default')
sns.set_palette("husl")
plt.rcParams['figure.figsize'] = (15, 8)
plt.rcParams['font.size'] = 10

print("Libraries loaded successfully!")

# Configuration
DATASET_PATH = "../../dataset_creation/squad_scheming_dataset.jsonl"
MODEL_NAME = "meta-llama/Llama-3.1-8B-Instruct"

# Load model and tokenizer
print(f"Loading model: {MODEL_NAME}")
tokenizer = AutoTokenizer.from_pretrained(MODEL_NAME)
model = AutoModelForCausalLM.from_pretrained(
    MODEL_NAME,
    output_attentions=True,
    attn_implementation="eager",  # Required for attention extraction
    torch_dtype=torch.float16 if torch.cuda.is_available() else torch.float32,
    device_map="auto" if torch.cuda.is_available() else None
)

# Add padding token if not present
if tokenizer.pad_token is None:
    tokenizer.pad_token = tokenizer.eos_token

device = next(model.parameters()).device
print(f"Model loaded on device: {device}")
print(f"Model has {model.config.num_hidden_layers} layers and {model.config.num_attention_heads} attention heads")

# Load dataset
def load_dataset(path):
    data = []
    with open(path, 'r', encoding='utf-8') as f:
        for line in f:
            data.append(json.loads(line.strip()))
    return data

dataset = load_dataset(DATASET_PATH)
print(f"Loaded {len(dataset)} examples from dataset")

# Display first example structure
print("\nExample structure:")
example = dataset[0]
for key, value in example.items():
    if isinstance(value, str) and len(value) > 100:
        print(f"{key}: {value[:100]}...")
    else:
        print(f"{key}: {value}")

def create_chat_prompt(system_prompt, user_prompt):
    """Create a chat-formatted prompt for Llama models."""
    # Use simple format that works reliably with base models
    return f"{system_prompt}\n{user_prompt}Answer:"

def generate_with_attention(prompt, max_new_tokens=5, layer_idx=-1, head_idx=0):
    """Generate text while capturing attention patterns for each generated token."""

    # Tokenize input
    inputs = tokenizer(prompt, return_tensors="pt", padding=True, truncation=True)
    input_ids = inputs["input_ids"].to(device)
    attention_mask = inputs["attention_mask"].to(device)

    input_length = input_ids.shape[1]
    input_tokens = tokenizer.convert_ids_to_tokens(input_ids[0])

    # Storage for generation data
    generated_tokens = []
    generation_attentions = []

    # Generate tokens one by one
    current_ids = input_ids.clone()

    for step in range(max_new_tokens):
        with torch.no_grad():
            outputs = model(
                current_ids,
                attention_mask=attention_mask,
                output_attentions=True,
                use_cache=False
            )

        # Get next token using sampling for more natural responses
        logits = outputs.logits[0, -1, :]
        # Apply temperature for more natural generation
        temperature = 0.7
        logits = logits / temperature
        probs = torch.softmax(logits, dim=-1)
        next_token_id = torch.multinomial(probs, 1).unsqueeze(0)  # Make it [1, 1]
        next_token = tokenizer.convert_ids_to_tokens([next_token_id.item()])[0]

        # Store generated token
        generated_tokens.append(next_token)

        # Extract attention from the specified layer and head
        # attentions shape: [batch, heads, seq_len, seq_len]
        layer_attention = outputs.attentions[layer_idx][0]  # [heads, seq_len, seq_len]
        head_attention = layer_attention[head_idx]  # [seq_len, seq_len]

        # Get attention from the last position (newly generated token) to all previous positions
        generation_attention = head_attention[-1, :].cpu().numpy()  # [seq_len]
        generation_attentions.append(generation_attention)

        # Update current_ids for next iteration
        current_ids = torch.cat([current_ids, next_token_id], dim=1)

        # Update attention mask
        attention_mask = torch.cat([
            attention_mask,
            torch.ones((1, 1), device=device, dtype=attention_mask.dtype)
        ], dim=1)

        # Stop if we hit EOS token or other stopping conditions
        if (next_token_id.item() == tokenizer.eos_token_id or
            next_token_id.item() == tokenizer.pad_token_id or
            next_token in ['<|end_of_text|>', '</s>', '<|endoftext|>']):
            break

        # Also stop on natural sentence endings for short responses
        if step >= 3 and next_token in ['.', '!', '?'] and len(generated_tokens) > 5:
            generated_tokens.append(next_token)
            generation_attentions.append(generation_attention)
            break

    return {
        'input_tokens': input_tokens,
        'input_length': input_length,
        'generated_tokens': generated_tokens,
        'generation_attentions': generation_attentions,
        'prompt': prompt
    }

def analyze_generation_attention(example_idx, layer_idx=-1, head_idx=0, max_tokens=5):
    """Analyze attention patterns for both scheming and baseline scenarios."""

    if example_idx >= len(dataset):
        print(f"Example index {example_idx} out of range. Dataset has {len(dataset)} examples.")
        return None

    example = dataset[example_idx]

    print(f"\n{'='*80}")
    print(f"GENERATION ATTENTION ANALYSIS - EXAMPLE {example_idx}")
    print(f"{'='*80}")
    print(f"Pressure Category: {example.get('pressure_category', 'N/A')}")
    print(f"Pressure Source: {example.get('pressure_source', 'N/A')}")
    print(f"Ground Truth: {example.get('ground_truth', example.get('answer', 'N/A'))}")

    # Check for different possible key names for honest/dishonest answers
    honest_key = None
    dishonest_key = None

    for key in example.keys():
        if 'honest' in key.lower():
            honest_key = key
        elif 'dishonest' in key.lower():
            dishonest_key = key

    honest_answer = example.get(honest_key, example.get('answer', 'N/A')) if honest_key else example.get('answer', 'N/A')
    dishonest_answer = example.get(dishonest_key, 'N/A') if dishonest_key else 'N/A'

    print(f"Expected Honest: {honest_answer}")
    print(f"Expected Dishonest: {dishonest_answer}")
    print(f"Layer: {layer_idx} | Head: {head_idx}")

    # Debug: Print all available keys
    print(f"\nAvailable keys in example: {list(example.keys())}")

    # Create prompts - handle different possible key names
    scheming_system = example.get('scheming_system_prompt', example.get('system_prompt_scheming', example.get('system_prompt', 'You are a helpful assistant.')))
    scheming_user = example.get('user_prompt', example.get('prompt', example.get('question', 'Hello')))

    # Baseline always uses simple system prompt and combines original context + question
    baseline_system = 'You are a helpful assistant.'
    original_context = example.get('original_context', example.get('context', ''))
    original_question = example.get('original_question', example.get('question', example.get('user_prompt', 'Hello')))

    # Combine context and question for baseline user prompt
    if original_context:
        baseline_user = f"{original_context}\n\n{original_question}"
    else:
        baseline_user = original_question

    scheming_prompt = create_chat_prompt(scheming_system, scheming_user)
    baseline_prompt = create_chat_prompt(baseline_system, baseline_user)

    # Debug: Print the actual prompts being used
    print(f"\n🔧 DEBUG - SCHEMING PROMPT:")
    print(f"'{scheming_prompt[:200]}...'")
    print(f"\n🔧 DEBUG - BASELINE PROMPT:")
    print(f"'{baseline_prompt[:200]}...'")

    # Generate and analyze scheming scenario
    print(f"\n🔴 ANALYZING SCHEMING SCENARIO...")
    print(f"System: {scheming_system[:100]}...")
    print(f"User: {scheming_user}")

    scheming_data = generate_with_attention(scheming_prompt, max_tokens, layer_idx, head_idx)

    # Generate and analyze baseline scenario
    print(f"\n🔵 ANALYZING BASELINE SCENARIO...")
    print(f"System: {baseline_system}")
    print(f"User: {baseline_user}")

    baseline_data = generate_with_attention(baseline_prompt, max_tokens, layer_idx, head_idx)

    return {
        'scheming': {
            'generation_data': scheming_data,
            'system_prompt': scheming_system,
            'user_prompt': scheming_user
        },
        'baseline': {
            'generation_data': baseline_data,
            'system_prompt': baseline_system,
            'user_prompt': baseline_user
        },
        'metadata': {
            'example_idx': example_idx,
            'pressure_category': example.get('pressure_category', 'N/A'),
            'honest_answer': honest_answer,
            'dishonest_answer': dishonest_answer,
            'ground_truth': example.get('ground_truth', example.get('answer', 'N/A'))
        },
        'layer_idx': layer_idx,
        'head_idx': head_idx
    }

print("Core generation and attention analysis functions defined!")

def print_generation_attention_analysis(generation_data, head_idx=0, scenario_name=""):
    """Print detailed attention analysis for each generated token."""

    if not generation_data['generated_tokens']:
        print("No tokens were generated.")
        return

    input_tokens = generation_data['input_tokens']
    generated_tokens = generation_data['generated_tokens']
    generation_attentions = generation_data['generation_attentions']
    input_length = generation_data['input_length']

    generated_text = tokenizer.convert_tokens_to_string(generated_tokens)

    print(f"\n🎯 GENERATION ATTENTION ANALYSIS (Head {head_idx})")
    print(f"Input length: {input_length} tokens")
    print(f"Generated: {len(generated_tokens)} tokens")
    print(f"Generated text: \"{generated_text}\"")

    # Analyze each generated token
    for step, (token, attention_weights) in enumerate(zip(generated_tokens, generation_attentions)):
        print(f"\n📍 Step {step + 1}: Generated '{token}'")

        # Get attention to input tokens only
        input_attention = attention_weights[:input_length]

        # Find top attended input tokens
        top_indices = np.argsort(input_attention)[-5:][::-1]  # Top 5

        print(f"   Top attended input tokens:")
        for i, idx in enumerate(top_indices):
            if idx < len(input_tokens):
                token_text = tokenizer.convert_tokens_to_string([input_tokens[idx]])
                print(f"   {i+1:2d}. '{token_text}' (pos {idx}) - {input_attention[idx]:.4f}")

def create_colored_attention_visualization(generation_data, target_gen_step=0, head_idx=0, title=""):
    """Create colored visualization showing attention from a specific generated token to input."""

    if target_gen_step >= len(generation_data['generation_attentions']):
        print(f"Generation step {target_gen_step} out of range.")
        return None

    input_tokens = generation_data['input_tokens']
    generated_tokens = generation_data['generated_tokens']
    attention_weights = generation_data['generation_attentions'][target_gen_step]
    input_length = generation_data['input_length']

    # Get attention to input tokens only
    input_attention = attention_weights[:input_length]

    # Normalize attention weights to [0, 1], excluding BOS token from scale calculation
    if len(input_attention) > 0:
        # Identify BOS token (usually at position 0)
        bos_token = input_tokens[0] if len(input_tokens) > 0 else None
        is_bos = bos_token in ['<|begin_of_text|>', '<s>', '<|startoftext|>', '<bos>']

        # Calculate min/max excluding BOS token for better scale
        if is_bos and len(input_attention) > 1:
            # Exclude BOS token (position 0) from normalization calculation
            non_bos_attention = input_attention[1:]
            min_att = np.min(non_bos_attention)
            max_att = np.max(non_bos_attention)
            print(f"📊 Attention scale: excluding BOS token (value: {input_attention[0]:.4f})")
            print(f"   Non-BOS range: {min_att:.4f} to {max_att:.4f}")
        else:
            # No BOS token or only one token, use all values
            min_att = np.min(input_attention)
            max_att = np.max(input_attention)

        if max_att > min_att:
            normalized_weights = (input_attention - min_att) / (max_att - min_att)
        else:
            normalized_weights = np.ones_like(input_attention) * 0.5
    else:
        normalized_weights = np.array([])

    # Create colormap
    cmap = plt.cm.get_cmap('Reds')

    # Generate HTML
    target_token = generated_tokens[target_gen_step] if target_gen_step < len(generated_tokens) else "N/A"
    target_text = tokenizer.convert_tokens_to_string([target_token])

    html_parts = []
    html_parts.append(f'<div style="font-family: monospace; font-size: 14px; line-height: 1.8; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #fafafa; max-width: 100%; overflow-wrap: break-word;">')
    html_parts.append(f'<h3 style="margin-top: 0; color: #333;">{title} (Head {head_idx})</h3>')
    html_parts.append(f'<div style="margin-bottom: 10px; font-weight: bold;">Attention from Generated Token \"{target_text}\" (Step {target_gen_step + 1}) to Input:</div>')

    def clean_token_display(token):
        """Clean token for display by removing encoding artifacts and special characters."""
        # Remove common encoding artifacts
        cleaned = token.replace('▁', ' ').replace('Ġ', ' ')
        cleaned = cleaned.replace('âĢ', '').replace('Ċ', ' ')
        cleaned = cleaned.replace('ï»¿', '').replace('Â°', '°')
        cleaned = cleaned.replace('âĢ²', "'").replace('âĢ³', '"')

        # Remove other common artifacts
        cleaned = cleaned.replace('Â', '')

        # Clean up whitespace
        if cleaned.startswith(' '):
            cleaned = cleaned[1:]

        return cleaned if cleaned.strip() else token  # Fallback to original if empty

    for i, token in enumerate(input_tokens):
        # Skip BOS token (commonly has very high attention as an 'attention sink')
        if token in ['<|begin_of_text|>', '<s>', '<|startoftext|>', '<bos>'] and i == 0:
            # Add a note about skipped BOS token
            html_parts.append(
                f'<span style="background-color: #f0f0f0; padding: 2px 4px; margin: 1px; '
                f'border-radius: 3px; display: inline-block; word-break: break-all; '
                f'border: 1px dashed #ccc; color: #666;" '
                f'title="BOS token filtered out (typically has very high attention as attention sink)">'
                f'[BOS filtered]</span>'
            )
            continue

        if i < len(normalized_weights):
            # Get color from colormap
            rgba = cmap(normalized_weights[i])
            color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'

            # Clean token for display
            display_token = clean_token_display(token)

            # Create colored span with tooltip
            html_parts.append(
                f'<span style="background-color: {color}; padding: 2px 4px; margin: 1px; '
                f'border-radius: 3px; display: inline-block; word-break: break-all;" '
                f'title="Token: {display_token}\\nAttention: {input_attention[i]:.4f}\\nNormalized: {normalized_weights[i]:.4f}\\nPosition: {i}">{display_token}</span>'
            )
        else:
            # Token without attention weight
            display_token = clean_token_display(token)
            html_parts.append(f'<span style="padding: 2px 4px; margin: 1px; word-break: break-all;">{display_token}</span>')

    # Add colorbar legend
    html_parts.append('<div style="margin-top: 15px; font-size: 12px;">')
    html_parts.append('<strong>Attention Scale:</strong> ')

    # Create mini colorbar
    for i in range(10):
        intensity = i / 9.0
        rgba = cmap(intensity)
        color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'
        html_parts.append(f'<span style="background-color: {color}; width: 20px; height: 15px; display: inline-block; margin: 1px;"></span>')

    if len(input_attention) > 0:
        html_parts.append(f'<br><span style="font-size: 10px;">Min: {min_att:.4f} | Max: {max_att:.4f}</span>')

    html_parts.append('</div>')
    html_parts.append('</div>')

    return ''.join(html_parts)

def create_attention_heatmap(generation_data, head_idx=0, title="Generation Attention Heatmap"):
    """Create interactive heatmap showing attention from all generated tokens to input."""

    if not generation_data['generated_tokens']:
        print("No tokens were generated.")
        return None

    input_tokens = generation_data['input_tokens']
    generated_tokens = generation_data['generated_tokens']
    generation_attentions = generation_data['generation_attentions']
    input_length = generation_data['input_length']

    # Create attention matrix: [num_generated_tokens, input_length]
    attention_matrix = []
    for attention_weights in generation_attentions:
        input_attention = attention_weights[:input_length]
        attention_matrix.append(input_attention)

    attention_matrix = np.array(attention_matrix)

    # Prepare labels
    input_labels = [tokenizer.convert_tokens_to_string([token]).strip() for token in input_tokens[:input_length]]
    gen_labels = [f"Gen{i+1}: {tokenizer.convert_tokens_to_string([token]).strip()}" for i, token in enumerate(generated_tokens)]

    # Create heatmap
    fig = go.Figure(data=go.Heatmap(
        z=attention_matrix,
        x=input_labels,
        y=gen_labels,
        colorscale='Reds',
        hovertemplate='<b>Generated:</b> %{y}<br><b>Input:</b> %{x}<br><b>Attention:</b> %{z:.4f}<extra></extra>'
    ))

    fig.update_layout(
        title=f"{title} (Head {head_idx})",
        xaxis_title="Input Tokens",
        yaxis_title="Generated Tokens",
        width=max(800, len(input_labels) * 20),
        height=max(400, len(gen_labels) * 50)
    )

    fig.update_xaxes(tickangle=-45)

    return fig

print("Per-token attention analysis and visualization functions defined!")

def calculate_average_generation_attention(generation_data, head_idx=0):
    """Calculate average attention across all generated tokens."""

    if not generation_data['generated_tokens']:
        print("No generation data available.")
        return None

    input_length = generation_data['input_length']
    input_tokens = generation_data['input_tokens']
    generated_tokens = generation_data['generated_tokens']

    # Collect attention weights for all generated tokens
    all_attention_weights = []

    for attention_weights in generation_data['generation_attentions']:
        input_attention = attention_weights[:input_length]  # Only attention to input tokens
        all_attention_weights.append(input_attention)

    # Calculate average attention across all generated tokens
    if all_attention_weights:
        average_attention = np.mean(all_attention_weights, axis=0)  # [input_length]

        # Find top attended input tokens (excluding BOS token)
        bos_token = input_tokens[0] if len(input_tokens) > 0 else None
        is_bos = bos_token in ['<|begin_of_text|>', '<s>', '<|startoftext|>', '<bos>']

        if is_bos and len(average_attention) > 1:
            # Exclude BOS token (position 0) from top tokens ranking
            non_bos_attention = average_attention[1:]
            non_bos_indices = np.argsort(non_bos_attention)[-10:][::-1]  # Top 10 non-BOS
            top_indices = non_bos_indices + 1  # Adjust indices to account for skipped BOS
            print(f"📊 Top tokens analysis: excluding BOS token (attention: {average_attention[0]:.4f})")
        else:
            # No BOS token, use all tokens
            top_indices = np.argsort(average_attention)[-10:][::-1]  # Top 10

        analysis = {
            'average_attention': average_attention,
            'input_tokens': input_tokens,
            'generated_tokens': generated_tokens,
            'top_attended_tokens': []
        }

        for idx in top_indices:
            if idx < len(input_tokens):
                analysis['top_attended_tokens'].append({
                    'token': input_tokens[idx],
                    'text': tokenizer.convert_tokens_to_string([input_tokens[idx]]),
                    'attention': average_attention[idx],
                    'position': idx
                })

        return analysis

    return None

def create_average_attention_visualization(generation_data, head_idx=0, title="Average Attention Across All Generated Tokens"):
    """Create colored visualization showing average attention across all generated tokens."""

    analysis = calculate_average_generation_attention(generation_data, head_idx)

    if not analysis:
        print("No analysis data available.")
        return None

    input_tokens = analysis['input_tokens']
    average_attention = analysis['average_attention']
    generated_tokens = analysis['generated_tokens']

    # Normalize attention weights to [0, 1], excluding BOS token from scale calculation
    if len(average_attention) > 0:
        # Identify BOS token (usually at position 0)
        bos_token = input_tokens[0] if len(input_tokens) > 0 else None
        is_bos = bos_token in ['<|begin_of_text|>', '<s>', '<|startoftext|>', '<bos>']

        # Calculate min/max excluding BOS token for better scale
        if is_bos and len(average_attention) > 1:
            # Exclude BOS token (position 0) from normalization calculation
            non_bos_attention = average_attention[1:]
            min_att = np.min(non_bos_attention)
            max_att = np.max(non_bos_attention)
            print(f"📊 Average attention scale: excluding BOS token (value: {average_attention[0]:.4f})")
            print(f"   Non-BOS range: {min_att:.4f} to {max_att:.4f}")
        else:
            # No BOS token or only one token, use all values
            min_att = np.min(average_attention)
            max_att = np.max(average_attention)

        if max_att > min_att:
            normalized_weights = (average_attention - min_att) / (max_att - min_att)
        else:
            normalized_weights = np.ones_like(average_attention) * 0.5
    else:
        normalized_weights = np.array([])

    # Create colormap (use purple to distinguish from single-token analysis)
    cmap = plt.cm.get_cmap('Purples')

    # Generate HTML
    generated_text = tokenizer.convert_tokens_to_string(generated_tokens)
    html_parts = []
    html_parts.append(f'<div style="font-family: monospace; font-size: 14px; line-height: 1.8; padding: 20px; border: 1px solid #ddd; border-radius: 5px; background-color: #fafafa; max-width: 100%; overflow-wrap: break-word;">')
    html_parts.append(f'<h3 style="margin-top: 0; color: #333;">{title} (Head {head_idx})</h3>')
    html_parts.append(f'<div style="margin-bottom: 10px; font-weight: bold;">Generated Response: \"{generated_text}\"</div>')
    html_parts.append(f'<div style="margin-bottom: 15px; font-weight: bold;">Average Attention Across {len(generated_tokens)} Generated Tokens:</div>')

    for i, token in enumerate(input_tokens):
        # Skip BOS token (commonly has very high attention as an 'attention sink')
        if token in ['<|begin_of_text|>', '<s>', '<|startoftext|>'] and i == 0:
            # Add a note about skipped BOS token
            html_parts.append(
                f'<span style="background-color: #f0f0f0; padding: 2px 4px; margin: 1px; '
                f'border-radius: 3px; display: inline-block; word-break: break-all; '
                f'border: 1px dashed #ccc;" '
                f'title="BOS token filtered out (typically has very high attention as attention sink)">'
                f'[BOS filtered]</span>'
            )
            continue

        if i < len(normalized_weights):
            # Get color from colormap
            rgba = cmap(normalized_weights[i])
            color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'

            # Clean token for display
            display_token = token.replace('▁', ' ').replace('Ġ', ' ')
            if display_token.startswith(' '):
                display_token = display_token[1:]

            # Create colored span with tooltip
            html_parts.append(
                f'<span style="background-color: {color}; padding: 2px 4px; margin: 1px; '
                f'border-radius: 3px; display: inline-block; word-break: break-all;" '
                f'title="Token: {display_token}\\nAverage Attention: {average_attention[i]:.4f}\\nNormalized: {normalized_weights[i]:.4f}\\nPosition: {i}">{display_token}</span>'
            )
        else:
            # Token without attention weight
            display_token = token.replace('▁', ' ').replace('Ġ', ' ')
            if display_token.startswith(' '):
                display_token = display_token[1:]
            html_parts.append(f'<span style="padding: 2px 4px; margin: 1px; word-break: break-all;">{display_token}</span>')

    # Add colorbar legend
    html_parts.append('<div style="margin-top: 15px; font-size: 12px;">')
    html_parts.append('<strong>Average Attention Scale:</strong> ')

    # Create mini colorbar
    for i in range(10):
        intensity = i / 9.0
        rgba = cmap(intensity)
        color = f'rgba({int(rgba[0]*255)}, {int(rgba[1]*255)}, {int(rgba[2]*255)}, {rgba[3]})'
        html_parts.append(f'<span style="background-color: {color}; width: 20px; height: 15px; display: inline-block; margin: 1px;"></span>')

    if len(average_attention) > 0:
        html_parts.append(f'<br><span style="font-size: 10px;">Min: {min_att:.4f} | Max: {max_att:.4f}</span>')

    html_parts.append('</div>')
    html_parts.append('</div>')

    return ''.join(html_parts), analysis

def create_average_attention_comparison_plot(scheming_analysis, baseline_analysis, head_idx=0):
    """Create comparison plot of average attention patterns between scenarios."""

    if not scheming_analysis or not baseline_analysis:
        print("Missing analysis data for comparison.")
        return None

    # Get top tokens from both scenarios
    scheming_top = scheming_analysis['top_attended_tokens'][:15]
    baseline_top = baseline_analysis['top_attended_tokens'][:15]

    # Create comparison plot
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=["🔴 Scheming - Top Average Attention", "🔵 Baseline - Top Average Attention"],
        specs=[[{"type": "bar"}, {"type": "bar"}]]
    )

    # Scheming scenario
    scheming_tokens = [item['text'][:15] for item in scheming_top]
    scheming_attentions = [item['attention'] for item in scheming_top]

    fig.add_trace(
        go.Bar(
            x=scheming_tokens,
            y=scheming_attentions,
            name="Scheming",
            marker_color='red',
            hovertemplate='<b>Token:</b> %{x}<br><b>Avg Attention:</b> %{y:.4f}<extra></extra>'
        ),
        row=1, col=1
    )

    # Baseline scenario
    baseline_tokens = [item['text'][:15] for item in baseline_top]
    baseline_attentions = [item['attention'] for item in baseline_top]

    fig.add_trace(
        go.Bar(
            x=baseline_tokens,
            y=baseline_attentions,
            name="Baseline",
            marker_color='blue',
            hovertemplate='<b>Token:</b> %{x}<br><b>Avg Attention:</b> %{y:.4f}<extra></extra>'
        ),
        row=1, col=2
    )

    fig.update_layout(
        title=f"Average Attention Comparison Across All Generated Tokens (Head {head_idx})",
        showlegend=False,
        height=500
    )

    fig.update_xaxes(tickangle=-45)
    fig.update_yaxes(title_text="Average Attention")

    return fig

print("Average attention analysis functions defined!")

# Create interactive controls
example_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=min(len(dataset) - 1, 50),  # Limit to first 50 examples for performance
    step=1,
    description='Example:',
    style={'description_width': 'initial'}
)

layer_slider = widgets.IntSlider(
    value=-1,
    min=-1,
    max=31,  # Llama-3.1-8B has 32 layers (0-31), -1 means last layer
    step=1,
    description='Layer:',
    style={'description_width': 'initial'}
)

head_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=31,  # Llama-3.1-8B has 32 attention heads (0-31)
    step=1,
    description='Head:',
    style={'description_width': 'initial'}
)

max_tokens_slider = widgets.IntSlider(
    value=128,
    min=1,
    max=512,
    step=1,
    description='Max Tokens:',
    style={'description_width': 'initial'}
)

# Display controls
display(widgets.VBox([
    widgets.HTML('<h3>🎛️ Analysis Controls</h3>'),
    widgets.HBox([example_slider, layer_slider]),
    widgets.HBox([head_slider, max_tokens_slider]),
    widgets.HTML('<p><strong>Instructions:</strong> Adjust the sliders above and run the cell below to analyze attention patterns.</p>')
]))

print("Interactive controls created!")

# Run the analysis with current slider values
results = analyze_generation_attention(
    example_slider.value,
    layer_slider.value,
    head_slider.value,
    max_tokens_slider.value
)

if results:
    # Print detailed attention analysis for both scenarios
    print_generation_attention_analysis(
        results['scheming']['generation_data'],
        results['head_idx'],
        "Scheming"
    )

    print_generation_attention_analysis(
        results['baseline']['generation_data'],
        results['head_idx'],
        "Baseline"
    )

    print("\n✅ Generation attention analysis complete!")

    # Display generated responses prominently
    if (results['scheming']['generation_data']['generated_tokens'] and
        results['baseline']['generation_data']['generated_tokens']):

        scheming_response = tokenizer.convert_tokens_to_string(results['scheming']['generation_data']['generated_tokens'])
        baseline_response = tokenizer.convert_tokens_to_string(results['baseline']['generation_data']['generated_tokens'])

        print("\n" + "="*100)
        print("🎯 GENERATED RESPONSES COMPARISON")
        print("="*100)

        print(f"\n🔴 SCHEMING SCENARIO RESPONSE:")
        print(f"┌─ Generated {len(results['scheming']['generation_data']['generated_tokens'])} tokens ─┐")
        print(f"│ \"{scheming_response}\" │")
        print(f"└─────────────────────────────────────────────────────────────────────────────────────┘")

        print(f"\n🔵 BASELINE SCENARIO RESPONSE:")
        print(f"┌─ Generated {len(results['baseline']['generation_data']['generated_tokens'])} tokens ─┐")
        print(f"│ \"{baseline_response}\" │")
        print(f"└─────────────────────────────────────────────────────────────────────────────────────┘")

    print("\n📊 Scroll up to see detailed attention analysis for each generated token.")
else:
    print("❌ Analysis failed. Please check the example index and try again.")

# Assumed necessary imports for ipywidgets and IPython display
import ipywidgets as widgets
from IPython.display import display, HTML

# Main Average Attention Analysis with Token Selection
# Ensure the 'results' variable and custom functions like 'calculate_average_generation_attention'
# and 'create_average_attention_visualization' are defined before this code runs.

if 'results' in locals() and results and 'scheming' in results and 'baseline' in results:
    scheming_tokens = results['scheming']['generation_data']['generated_tokens']
    baseline_tokens = results['baseline']['generation_data']['generated_tokens']

    print("\n📊 ATTENTION ANALYSIS RESULTS")
    print("="*80)

    # --- Step 1: Show Average Attention First ---
    print("\n📈 AVERAGE ATTENTION ACROSS ALL GENERATED TOKENS")
    print("="*60)
    print("\n💡 Note: BOS token (<|begin_of_text|>) typically gets very high attention")
    print("    This is NORMAL behavior in transformers - it acts as an 'attention sink'")
    print("    Focus on the non-BOS tokens for meaningful content attention patterns.\n")

    # Show average attention visualizations
    scheming_avg_html, scheming_avg_analysis = create_average_attention_visualization(
        results['scheming']['generation_data'],
        results['head_idx'],
        "🔴 Scheming - Average Attention (BOS filtered)"
    )

    if scheming_avg_html:
        display(HTML(scheming_avg_html))

    baseline_avg_html, baseline_avg_analysis = create_average_attention_visualization(
        results['baseline']['generation_data'],
        results['head_idx'],
        "🔵 Baseline - Average Attention (BOS filtered)"
    )

    if baseline_avg_html:
        display(HTML(baseline_avg_html))

    # Create comparison plot
    if scheming_avg_analysis and baseline_avg_analysis:
        comparison_fig = create_average_attention_comparison_plot(
            scheming_avg_analysis,
            baseline_avg_analysis,
            results['head_idx']
        )

        if comparison_fig:
            comparison_fig.show()

    # --- Step 2: Show Individual Token Attention ---
    print("\n\n🔍 INDIVIDUAL TOKEN ATTENTION ANALYSIS")
    print("="*60)

    # Show scheming tokens
    print("\n🔴 SCHEMING SCENARIO TOKENS:")
    print("-" * 40)

    for i, token in enumerate(scheming_tokens):
        clean_token = token.replace('▁', ' ').replace('Ġ', ' ').strip()

        print(f"\n🔴 SCHEMING TOKEN {i + 1}: \"{clean_token}\"")
        print("=" * 50)

        scheming_html = create_colored_attention_visualization(
            results['scheming']['generation_data'],
            target_gen_step=i,
            head_idx=results['head_idx'],
            title=f"🔴 Scheming - Token {i + 1} (\"{clean_token}\") Attention"
        )

        if scheming_html:
            display(HTML(scheming_html))

    # Show baseline tokens
    print("\n\n🔵 BASELINE SCENARIO TOKENS:")
    print("-" * 40)

    for i, token in enumerate(baseline_tokens):
        clean_token = token.replace('▁', ' ').replace('Ġ', ' ').strip()

        print(f"\n🔵 BASELINE TOKEN {i + 1}: \"{clean_token}\"")
        print("=" * 50)

        baseline_html = create_colored_attention_visualization(
            results['baseline']['generation_data'],
            target_gen_step=i,
            head_idx=results['head_idx'],
            title=f"🔵 Baseline - Token {i + 1} (\"{clean_token}\") Attention"
        )

        if baseline_html:
            display(HTML(baseline_html))

    print("\n\n✅ ATTENTION ANALYSIS COMPLETE!")
    print("💡 Summary:")
    print(f"   📊 Analyzed {len(scheming_tokens)} scheming tokens and {len(baseline_tokens)} baseline tokens")
    print(f"   🎯 Head {results['head_idx']} attention patterns")
    print(f"   🔍 BOS tokens filtered for cleaner analysis")

else:
    print("❌ No analysis results available. Please run the generation analysis first.")


# Create interactive heatmaps
if 'results' in locals() and results:
    print("\n📊 INTERACTIVE ATTENTION HEATMAPS")

    # Scheming heatmap
    scheming_heatmap = create_attention_heatmap(
        results['scheming']['generation_data'],
        results['head_idx'],
        "🔴 Scheming - Generation Attention Matrix"
    )

    if scheming_heatmap:
        scheming_heatmap.show()

    # Baseline heatmap
    baseline_heatmap = create_attention_heatmap(
        results['baseline']['generation_data'],
        results['head_idx'],
        "🔵 Baseline - Generation Attention Matrix"
    )

    if baseline_heatmap:
        baseline_heatmap.show()

    print("\n💡 How to read the heatmaps:")
    print("- Y-axis: Generated tokens (Gen1, Gen2, etc.)")
    print("- X-axis: Input tokens")
    print("- Color intensity: Attention strength")
    print("- Hover over cells for exact attention values")

else:
    print("❌ No analysis results available. Please run the analysis first.")

# 📊 PERPLEXITY ANALYSIS FUNCTIONS
import torch # Assuming torch is used

# It's assumed that 'tokenizer', 'model', and 'device' are defined elsewhere
# tokenizer = ...
# model = ...
# device = ...

def calculate_token_perplexity(prompt, generated_tokens, input_length):
    """Calculate per-token perplexity for generated tokens."""

    # Tokenize the full sequence (input + generated)
    # The replacement logic for 'Ġ' is common for BPE tokenizers
    full_text = prompt + ''.join([token.replace('Ġ', ' ') for token in generated_tokens])
    inputs = tokenizer(full_text, return_tensors="pt", truncation=True)
    input_ids = inputs["input_ids"].to(device)

    # Get the generated token IDs
    generated_token_ids = input_ids[0][input_length:]

    if len(generated_token_ids) == 0:
        return {'per_token': [], 'average': float('inf'), 'tokens': [], 'log_probs': []}

    # Calculate logits for the sequence
    with torch.no_grad():
        outputs = model(input_ids, labels=input_ids)
        logits = outputs.logits

    # Get logits for predicting generated tokens
    generated_logits = logits[0, input_length-1:-1]

    # Calculate log probabilities
    log_probs = torch.nn.functional.log_softmax(generated_logits, dim=-1)
    token_log_probs = log_probs.gather(1, generated_token_ids.unsqueeze(1)).squeeze(1)

    # Convert to perplexity (lower is better)
    per_token_perplexity = torch.exp(-token_log_probs).cpu().numpy()
    average_perplexity = torch.exp(-token_log_probs.mean()).item()

    # Clean token names for display
    clean_tokens = [token.replace('Ġ', ' ') for token in generated_tokens]

    return {
        'per_token': per_token_perplexity.tolist(),
        'average': average_perplexity,
        'tokens': clean_tokens,
        'log_probs': token_log_probs.cpu().numpy().tolist()
    }

def print_perplexity_analysis(scheming_data, baseline_data):
    """Print detailed perplexity comparison between scenarios."""

    print(f"\n{'='*80}")
    print("📊 PERPLEXITY ANALYSIS")
    print(f"{'='*80}")

    # Calculate perplexity for both scenarios
    scheming_ppl = calculate_token_perplexity(
        scheming_data['prompt'],
        scheming_data['generated_tokens'],
        scheming_data['input_length']
    )

    baseline_ppl = calculate_token_perplexity(
        baseline_data['prompt'],
        baseline_data['generated_tokens'],
        baseline_data['input_length']
    )

    # Print scheming scenario perplexity
    print("\n🎭 SCHEMING SCENARIO PERPLEXITY:")
    print(f"Generated text: {''.join(scheming_ppl['tokens'])}")
    print(f"Average perplexity: {scheming_ppl['average']:.3f}")
    print("Per-token perplexity:")
    for i, (token, ppl) in enumerate(zip(scheming_ppl['tokens'], scheming_ppl['per_token'])):
        print(f"  {i+1:2d}. '{token}' → {ppl:.3f}")

    # Print baseline scenario perplexity
    print("\n🎯 BASELINE SCENARIO PERPLEXITY:")
    print(f"Generated text: {''.join(baseline_ppl['tokens'])}")
    print(f"Average perplexity: {baseline_ppl['average']:.3f}")
    print("Per-token perplexity:")
    for i, (token, ppl) in enumerate(zip(baseline_ppl['tokens'], baseline_ppl['per_token'])):
        print(f"  {i+1:2d}. '{token}' → {ppl:.3f}")

    # Comparison
    print("\n📈 COMPARISON:")
    avg_diff = scheming_ppl['average'] - baseline_ppl['average']
    print(f"Average perplexity difference: {avg_diff:+.3f}")
    if avg_diff > 0:
        print("   → Scheming scenario has HIGHER perplexity (model less confident)")
    elif avg_diff < 0:
        print("   → Scheming scenario has LOWER perplexity (model more confident)")
    else:
        print("   → Similar confidence levels")

    # Token-by-token comparison
    print("\n🔍 TOKEN-BY-TOKEN COMPARISON:")
    max_len = max(len(scheming_ppl['per_token']), len(baseline_ppl['per_token']))

    print(f"{'Pos':<4} {'Scheming Token':<15} {'Scheming PPL':<13} {'Baseline Token':<15} {'Baseline PPL':<13} {'Difference'}")
    print("-" * 80)

    for i in range(max_len):
        s_token = scheming_ppl['tokens'][i] if i < len(scheming_ppl['tokens']) else 'N/A'
        s_ppl = scheming_ppl['per_token'][i] if i < len(scheming_ppl['per_token']) else float('inf')
        b_token = baseline_ppl['tokens'][i] if i < len(baseline_ppl['tokens']) else 'N/A'
        b_ppl = baseline_ppl['per_token'][i] if i < len(baseline_ppl['per_token']) else float('inf')

        # FIX: Check for infinity before formatting the strings to prevent runtime error
        s_ppl_str = f"{s_ppl:.3f}" if s_ppl != float('inf') else "inf"
        b_ppl_str = f"{b_ppl:.3f}" if b_ppl != float('inf') else "inf"

        diff_str = "N/A"
        if s_ppl != float('inf') and b_ppl != float('inf'):
            diff = s_ppl - b_ppl
            diff_str = f"{diff:+.3f}"

        print(f"{i+1:<4} {s_token:<15} {s_ppl_str:<13} {b_token:<15} {b_ppl_str:<13} {diff_str}")

    return scheming_ppl, baseline_ppl

print("✅ Perplexity analysis functions loaded!")
print("\n📊 Available functions:")
print("   • calculate_token_perplexity() - Calculate per-token perplexity")
print("   • print_perplexity_analysis()  - Print detailed comparison")

# 🚀 RUN PERPLEXITY ANALYSIS

# First, run the attention analysis to get the data
if 'analysis_results' in globals() and analysis_results is not None:
    print("📊 Running perplexity analysis on existing results...")

    scheming_data = analysis_results['scheming']
    baseline_data = analysis_results['baseline']

    # Print detailed perplexity analysis
    scheming_ppl, baseline_ppl = print_perplexity_analysis(scheming_data, baseline_data)

    # Create visualization
    print("\📈 Creating perplexity visualization...")

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

    # Per-token perplexity comparison
    max_len = max(len(scheming_ppl['per_token']), len(baseline_ppl['per_token']))
    positions = range(1, max_len + 1)

    # Pad shorter sequence with NaN
    s_ppls = scheming_ppl['per_token'] + [np.nan] * (max_len - len(scheming_ppl['per_token']))
    b_ppls = baseline_ppl['per_token'] + [np.nan] * (max_len - len(baseline_ppl['per_token']))

    ax1.bar([p - 0.2 for p in positions], s_ppls, width=0.4, label='Scheming', alpha=0.7, color='red')
    ax1.bar([p + 0.2 for p in positions], b_ppls, width=0.4, label='Baseline', alpha=0.7, color='blue')
    ax1.set_xlabel('Token Position')
    ax1.set_ylabel('Perplexity')
    ax1.set_title('Per-Token Perplexity Comparison')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # Average perplexity comparison
    scenarios = ['Scheming', 'Baseline']
    avg_ppls = [scheming_ppl['average'], baseline_ppl['average']]
    colors = ['red', 'blue']

    bars = ax2.bar(scenarios, avg_ppls, color=colors, alpha=0.7)
    ax2.set_ylabel('Average Perplexity')
    ax2.set_title('Average Perplexity Comparison')
    ax2.grid(True, alpha=0.3)

    # Add value labels on bars
    for bar, value in zip(bars, avg_ppls):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                f'{value:.2f}', ha='center', va='bottom')

    plt.tight_layout()
    plt.show()

    print("\✅ Perplexity analysis complete!")

else:
    print("❌ No analysis results found. Please run the attention analysis first.")
    print("\💡 To run perplexity analysis:")
    print("   1. Run the attention analysis cell above")
    print("   2. Then run this cell to see perplexity comparison")