{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🧠 Attention Pattern Analysis: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON>\n", "\n", "This notebook provides BertViz visualizations comparing attention patterns between scheming and baseline scenarios.\n", "\n", "## Quick Start\n", "1. Run all cells in order\n", "2. Use the interactive widgets to explore different examples\n", "3. Click visualization buttons to see attention patterns"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install and import dependencies\n", "!pip install bertviz transformers torch accelerate datasets ipywidgets matplotlib seaborn plotly\n", "\n", "import json\n", "import torch\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from typing import Dict, List, Tuple, Optional, Any\n", "import warnings\n", "from pathlib import Path\n", "from transformers import AutoTokenizer, AutoModelForCausalLM, GenerationConfig\n", "from bertviz import head_view, model_view\n", "import ipywidgets as widgets\n", "from IPython.display import display, HTML, clear_output\n", "\n", "warnings.filterwarnings('ignore')\n", "plt.style.use('seaborn-v0_8')\n", "print(\"✅ Dependencies imported!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Load analysis module and setup\n", "exec(open('attention_analysis.py').read())\n", "\n", "# Setup GPU\n", "device, device_map = setup_gpu_environment()\n", "monitor_gpu_memory(\"Initial\")\n", "\n", "# Load dataset\n", "dataset_path = \"dataset_creation/squad_scheming_dataset.jsonl\"\n", "loader = SchemeDatasetLoader(dataset_path)\n", "dataset = loader.load_and_validate()\n", "print(f\"\\n🎯 Dataset loaded: {len(dataset)} examples\")\n", "\n", "# Load model\n", "analyzer = LlamaAttentionAnalyzer()\n", "model, tokenizer = analyzer.load_model()\n", "print(\"\\n🎉 Model loaded!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Attention generation function\n", "def generate_with_attention(prompt: str, max_new_tokens: int = 50):\n", "    \"\"\"Generate response with attention capture for <PERSON><PERSON><PERSON>.\"\"\"\n", "    \n", "    inputs = tokenizer(prompt, return_tensors=\"pt\", padding=True, truncation=True, max_length=2048)\n", "    if not device_map:\n", "        inputs = {k: v.to(device) for k, v in inputs.items()}\n", "    \n", "    input_length = inputs['input_ids'].shape[1]\n", "    \n", "    generation_config = GenerationConfig(\n", "        max_new_tokens=max_new_tokens,\n", "        do_sample=False,\n", "        temperature=1.0,\n", "        repetition_penalty=1.1,\n", "        pad_token_id=tokenizer.pad_token_id,\n", "        eos_token_id=tokenizer.eos_token_id,\n", "        output_attentions=True,\n", "        return_dict_in_generate=True,\n", "        use_cache=False\n", "    )\n", "    \n", "    with torch.no_grad():\n", "        outputs = model.generate(**inputs, generation_config=generation_config)\n", "    \n", "    generated_ids = outputs.sequences[0][input_length:]\n", "    generated_text = tokenizer.decode(generated_ids, skip_special_tokens=True)\n", "    full_sequence = outputs.sequences[0]\n", "    full_tokens = tokenizer.convert_ids_to_tokens(full_sequence)\n", "    \n", "    # Clean tokens\n", "    clean_tokens = []\n", "    for token in full_tokens:\n", "        if token.startswith('Ġ'):\n", "            clean_tokens.append(' ' + token[1:])\n", "        else:\n", "            clean_tokens.append(token)\n", "    \n", "    # Extract attention\n", "    if outputs.attentions:\n", "        last_step_attention = outputs.attentions[-1]\n", "        attention_tensor = torch.stack(last_step_attention, dim=0)\n", "    else:\n", "        attention_tensor = None\n", "    \n", "    return {\n", "        'generated_text': generated_text,\n", "        'input_length': input_length,\n", "        'attention': attention_tensor,\n", "        'tokens': clean_tokens,\n", "        'token_ids': full_sequence.tolist()\n", "    }\n", "\n", "print(\"✅ Attention generation ready!\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# BertViz visualization function\\n", "def show_attention_comparison(example_idx: int = 0, layer: int = 16, max_tokens: int = 30):\\n", "    \\\"\\\"\\\"Show side-by-side attention comparison.\\\"\\\"\\\"\\n", "    \\n", "    if example_idx >= len(dataset):\\n", "        print(f\\\"❌ Example index {example_idx} out of range\\\")\\n", "        return\\n", "    \\n", "    example = dataset[example_idx]\\n", "    print(f\\\"🔍 Analyzing Example {example_idx + 1}: {example.get('pressure_category', 'Unknown')}\\\")\\n", "    print(\\\"=\\\" * 80)\\n", "    \\n", "    # Construct scenarios\\n", "    scheming_scenario = analyzer.apply_chat_template(\\n", "        system_prompt=example['system_prompt'],\\n", "        user_prompt=example['user_prompt']\\n", "    )\\n", "    \\n", "    baseline_user_prompt = f\\\"Context: {example['original_context']}\\\\n\\\\nQuestion: {example['original_question']}\\\"\\n", "    baseline_scenario = analyzer.apply_chat_template(\\n", "        system_prompt=\\\"You are a helpful assistant.\\\",\\n", "        user_prompt=baseline_user_prompt\\n", "    )\\n", "    \\n", "    print(\\\"🔄 Generating scheming scenario response...\\\")\\n", "    scheming_result = generate_with_attention(scheming_scenario, max_tokens)\\n", "    \\n", "    print(\\\"🔄 Generating baseline scenario response...\\\")\\n", "    baseline_result = generate_with_attention(baseline_scenario, max_tokens)\\n", "    \\n", "    # Display results\\n", "    print(f\\\"\\\\n🎭 SCHEMING RESPONSE: {scheming_result['generated_text']}\\\")\\n", "    print(f\\\"🎯 BASELINE RESPONSE: {baseline_result['generated_text']}\\\")\\n", "    \\n", "    # Show BertViz attention visualizations\\n", "    if scheming_result['attention'] is not None and layer < scheming_result['attention'].shape[0]:\\n", "        print(f\\\"\\\\n👁️ ATTENTION VISUALIZATION (Layer {layer}):\\\")\\n", "        \\n", "        try:\\n", "            # Scheming attention\\n", "            scheming_attention = scheming_result['attention'][layer].unsqueeze(0)\\n", "            scheming_tokens = scheming_result['tokens']\\n", "            \\n", "            print(\\\"\\\\n🎭 Scheming Scenario Attention (Head View):\\\")\\n", "            head_view(scheming_attention, scheming_tokens)\\n", "            \\n", "            # Baseline attention\\n", "            if baseline_result['attention'] is not None:\\n", "                baseline_attention = baseline_result['attention'][layer].unsqueeze(0)\\n", "                baseline_tokens = baseline_result['tokens']\\n", "                \\n", "                print(\\\"\\\\n🎯 Baseline Scenario Attention (Head View):\\\")\\n", "                head_view(baseline_attention, baseline_tokens)\\n", "            \\n", "        except Exception as e:\\n", "            print(f\\\"❌ Error showing attention: {e}\\\")\\n", "    \\n", "    return scheming_result, baseline_result\\n", "\\n", "print(\\\"✅ BertViz visualization function ready!\\\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Interactive controls for BertViz\\n", "example_slider = widgets.IntSlider(\\n", "    value=0, min=0, max=len(dataset)-1, step=1,\\n", "    description='Example:', style={'description_width': 'initial'}\\n", ")\\n", "\\n", "layer_slider = widgets.IntSlider(\\n", "    value=16, min=0, max=31, step=1,\\n", "    description='Layer:', style={'description_width': 'initial'}\\n", ")\\n", "\\n", "tokens_slider = widgets.IntSlider(\\n", "    value=30, min=10, max=50, step=5,\\n", "    description='Max tokens:', style={'description_width': 'initial'}\\n", ")\\n", "\\n", "viz_button = widgets.Button(\\n", "    description='🧠 Show BertViz Attention', button_style='primary'\\n", ")\\n", "\\n", "output_area = widgets.Output()\\n", "\\n", "def on_viz_click(button):\\n", "    with output_area:\\n", "        clear_output(wait=True)\\n", "        show_attention_comparison(example_slider.value, layer_slider.value, tokens_slider.value)\\n", "\\n", "viz_button.on_click(on_viz_click)\\n", "\\n", "# Display interface\\n", "interface = widgets.VBox([\\n", "    widgets.HTML(\\\"<h2>🎛️ BertViz Attention Visualization</h2>\\\"),\\n", "    widgets.HBox([example_slider, layer_slider, tokens_slider]),\\n", "    viz_button,\\n", "    output_area\\n", "])\\n", "\\n", "display(interface)\\n", "\\n", "print(\\\"🎉 VISUALIZATIONS ARE HERE! ⬆️\\\")\\n", "print(\\\"\\n✅ Click the '🧠 Show BertViz Attention' button above to see:\\\")\\n", "print(\\\"   • Complete model responses (not truncated)\\\")\\n", "print(\\\"   • BertViz head view showing ALL attention heads\\\")\\n", "print(\\\"   • Side-by-side comparison of scheming vs baseline\\\")\\n", "print(\\\"   • Interactive layer and example selection\\\")\\n", "print(\\\"\\n🎯 The visualizations will appear in the output area above!\\\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}