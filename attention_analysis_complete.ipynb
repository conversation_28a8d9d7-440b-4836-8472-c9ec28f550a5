{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🧠 Attention Pattern Analysis: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON>\n", "\n", "## 📋 Core Purpose\n", "This notebook provides comprehensive analysis of attention patterns in scheming vs. baseline scenarios using BertViz visualizations. It generates complete model responses (not truncated at prompts) and provides clean, interactive output formatting with proper chat template application.\n", "\n", "## 🎯 Primary Objectives\n", "1. Compare attention patterns between scheming (pressure) and baseline (neutral) scenarios\n", "2. Generate complete model responses with proper chat template formatting\n", "3. Visualize attention patterns using BertViz head view and model view\n", "4. Analyze response quality through per-token and average perplexity calculations\n", "5. Provide interactive controls for exploring different dataset examples\n", "\n", "## 🚀 Quick Start\n", "Run all cells in order. The notebook will:\n", "- Load the Llama-3.1-8B-Instruct model with multi-GPU support\n", "- Load and validate the scheming dataset\n", "- Generate responses for both scheming and baseline scenarios\n", "- Visualize attention patterns with <PERSON><PERSON><PERSON>\n", "- Analyze perplexity and response quality"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 📦 Installation and Setup"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Install required packages\n", "!pip install bertviz transformers torch accelerate datasets ipywidgets matplotlib seaborn plotly\n", "\n", "# Enable widget extensions for Ju<PERSON><PERSON>\n", "!jupyter nbextension enable --py widgetsnbextension --sys-prefix"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 🔧 Import Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import json\n", "import torch\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import pandas as pd\n", "from typing import Dict, List, Tuple, Optional, Any\n", "import warnings\n", "from pathlib import Path\n", "import logging\n", "from tqdm.auto import tqdm\n", "\n", "# Transformers and model loading\n", "from transformers import (\n", "    AutoTokenizer, \n", "    AutoModelForCausalLM,\n", "    GenerationConfig\n", ")\n", "\n", "# BertViz for attention visualization\n", "from bertviz import head_view, model_view\n", "from bertviz.util import format_attention\n", "\n", "# Interactive widgets\n", "import ipywidgets as widgets\n", "from IPython.display import display, HTML, clear_output\n", "\n", "# Configure logging and warnings\n", "logging.basicConfig(level=logging.INFO)\n", "warnings.filterwarnings(\"ignore\", category=UserWarning)\n", "\n", "# Set style for better plots\n", "plt.style.use(\"seaborn-v0_8\")\n", "sns.set_palette(\"husl\")\n", "\n", "print(\"✅ All dependencies imported successfully!\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}