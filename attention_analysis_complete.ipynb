{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 🧠 Attention Pattern Analysis: Scheming vs. Baseline Scenarios\n",
    "\n",
    "## 📋 Core Purpose\n",
    "This notebook provides comprehensive analysis of attention patterns in scheming vs. baseline scenarios using Bert<PERSON>iz visualizations. It generates complete model responses (not truncated at prompts) and provides clean, interactive output formatting with proper chat template application.\n",
    "\n",
    "## 🎯 Primary Objectives\n",
    "1. Compare attention patterns between scheming (pressure) and baseline (neutral) scenarios\n",
    "2. Generate complete model responses with proper chat template formatting\n",
    "3. Visualize attention patterns using BertViz head view and model view\n",
    "4. Analyze response quality through per-token and average perplexity calculations\n",
    "5. Provide interactive controls for exploring different dataset examples\n",
    "\n",
    "## 🚀 Quick Start\n",
    "Run all cells in order. The notebook will:\n",
    "- Load the Llama-3.1-8B-Instruct model with multi-GPU support\n",
    "- Load and validate the scheming dataset\n",
    "- Generate responses for both scheming and baseline scenarios\n",
    "- Visualize attention patterns with <PERSON><PERSON><PERSON>\n",
    "- Analyze perplexity and response quality"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📦 Installation and Setup"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Install required packages\n",
    "!pip install bertviz transformers torch accelerate datasets ipywidgets matplotlib seaborn plotly\n",
    "\n",
    "# Enable widget extensions for Jupyter\n",
    "!jupyter nbextension enable --py widgetsnbextension --sys-prefix"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🔧 Import Dependencies"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "import json\n",
    "import torch\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "import pandas as pd\n",
    "from typing import Dict, List, Tuple, Optional, Any\n",
    "import warnings\n",
    "from pathlib import Path\n",
    "import logging\n",
    "from tqdm.auto import tqdm\n",
    "\n",
    "# Transformers and model loading\n",
    "from transformers import (\n",
    "    AutoTokenizer, \n",
    "    AutoModelForCausalLM,\n",
    "    GenerationConfig\n",
    ")\n",
    "\n",
    "# BertViz for attention visualization\n",
    "from bertviz import head_view, model_view\n",
    "from bertviz.util import format_attention\n",
    "\n",
    "# Interactive widgets\n",
    "import ipywidgets as widgets\n",
    "from IPython.display import display, HTML, clear_output\n",
    "\n",
    "# Configure logging and warnings\n",
    "logging.basicConfig(level=logging.INFO)\n",
    "warnings.filterwarnings('ignore', category=UserWarning)\n",
    "\n",
    "# Set style for better plots\n",
    "plt.style.use('seaborn-v0_8')\n",
    "sns.set_palette(\"husl\")\n",
    "\n",
    "print(\"✅ All dependencies imported successfully!\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🖥️ GPU Setup and Memory Monitoring"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def setup_gpu_environment():\n",
    "    \"\"\"Setup GPU environment with proper detection and memory monitoring.\"\"\"\n",
    "    \n",
    "    # CUDA availability check\n",
    "    if torch.cuda.is_available():\n",
    "        num_gpus = torch.cuda.device_count()\n",
    "        print(f\"🚀 CUDA Available: {num_gpus} GPU(s) detected\")\n",
    "        \n",
    "        # Display GPU information\n",
    "        for i in range(num_gpus):\n",
    "            gpu_name = torch.cuda.get_device_name(i)\n",
    "            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1e9\n",
    "            print(f\"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)\")\n",
    "        \n",
    "        device = \"cuda\"\n",
    "        device_map = \"auto\"  # Multi-GPU support\n",
    "    else:\n",
    "        print(\"⚠️  CUDA not available, using CPU (will be slow)\")\n",
    "        device = \"cpu\"\n",
    "        device_map = None\n",
    "    \n",
    "    return device, device_map\n",
    "\n",
    "def monitor_gpu_memory(stage: str):\n",
    "    \"\"\"Monitor and display GPU memory usage.\"\"\"\n",
    "    if torch.cuda.is_available():\n",
    "        for i in range(torch.cuda.device_count()):\n",
    "            allocated = torch.cuda.memory_allocated(i) / 1e9\n",
    "            cached = torch.cuda.memory_reserved(i) / 1e9\n",
    "            total = torch.cuda.get_device_properties(i).total_memory / 1e9\n",
    "            print(f\"📊 {stage} - GPU {i}: {allocated:.1f}GB allocated, {cached:.1f}GB cached, {total:.1f}GB total\")\n",
    "\n",
    "# Setup environment\\n",
    "    "device, device_map = setup_gpu_environment()\\n",
    "    "monitor_gpu_memory(\\"Initial\\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📊 Dataset Loading and Validation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load the attention analysis module\\n",
    "exec(open('attention_analysis.py').read())\\n",
    "\\n",
    "# Load the dataset\\n",
    "dataset_path = \\"dataset_creation/squad_scheming_dataset.jsonl\\"\\n",
    "loader = SchemeDatasetLoader(dataset_path)\\n",
    "dataset = loader.load_and_validate()\\n",
    "\\n",
    "print(f\\"\\\\n🎯 Dataset loaded successfully with {len(dataset)} examples!\\")\\n",
    "\\n",
    "# Create interactive filter widget\\n",
    "category_options = [\\"All\\"] + list(loader.stats['pressure_categories'].keys())\\n",
    "category_filter = widgets.Dropdown(\\n",
    "    options=category_options,\\n",
    "    value=\\"All\\",\\n",
    "    description=\\"Filter by category:\\",\\n",
    "    style={'description_width': 'initial'}\\n",
    ")\\n",
    "\\n",
    "def update_filter(change):\\n",
    "    filtered_data = loader.get_filtered_data(change['new'])\\n",
    "    print(f\\"📊 Current selection: {len(filtered_data)} examples\\")\\n",
    "\\n",
    "category_filter.observe(update_filter, names='value')\\n",
    "display(category_filter)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🤖 Model Loading and Configuration"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Initialize and load the model\\n",
    "analyzer = LlamaAttentionAnalyzer()\\n",
    "model, tokenizer = analyzer.load_model()\\n",
    "\\n",
    "print(\\"\\\\n🎉 Model setup complete!\\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 💬 Scenario Construction and Response Generation"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "class ScenarioGenerator:\\n",
    "    \\"\\"\\"Generate and analyze scheming vs baseline scenarios.\\"\\"\\"\\n",
    "    \\n",
    "    def __init__(self, model, tokenizer, analyzer):\\n",
    "        self.model = model\\n",
    "        self.tokenizer = tokenizer\\n",
    "        self.analyzer = analyzer\\n",
    "        \\n",
    "    def construct_scenarios(self, example: Dict[str, Any]) -> Tuple[str, str]:\\n",
    "        \\"\\"\\"Construct scheming and baseline scenarios from dataset example.\\"\\"\\"\\n",
    "        \\n",
    "        # Scheming scenario: Use dataset prompts exactly as provided\\n",
    "        scheming_scenario = self.analyzer.apply_chat_template(\\n",
    "            system_prompt=example['system_prompt'],\\n",
    "            user_prompt=example['user_prompt']\\n",
    "        )\\n",
    "        \\n",
    "        # Baseline scenario: Use neutral system prompt with original context/question\\n",
    "        baseline_user_prompt = f\\"Context: {example['original_context']}\\\\n\\\\nQuestion: {example['original_question']}\\"\\n",
    "        baseline_scenario = self.analyzer.apply_chat_template(\\n",
    "            system_prompt=\\"You are a helpful assistant.\\",\\n",
    "            user_prompt=baseline_user_prompt\\n",
    "        )\\n",
    "        \\n",
    "        return scheming_scenario, baseline_scenario\\n",
    "        \\n",
    "    def apply_chat_template(self, system_prompt: str, user_prompt: str) -> str:\\n",
    "        \\"\\"\\"Apply chat template with fallback to manual formatting.\\"\\"\\"\\n",
    "        \\n",
    "        messages = [\\n",
    "            {\\"role\\": \\"system\\", \\"content\\": system_prompt},\\n",
    "            {\\"role\\": \\"user\\", \\"content\\": user_prompt}\\n",
    "        ]\\n",
    "        \\n",
    "        try:\\n",
    "            # Try using the tokenizer's chat template\\n",
    "            formatted = self.tokenizer.apply_chat_template(\\n",
    "                messages,\\n",
    "                tokenize=False,\\n",
    "                add_generation_prompt=True\\n",
    "            )\\n",
    "            return formatted\\n",
    "        except Exception as e:\\n",
    "            print(f\\"⚠️  Chat template failed, using manual formatting: {e}\\")\\n",
    "            # Fallback to manual Llama format\\n",
    "            return f\\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\\\n\\\\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\\\\n\\\\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\\\n\\\\n\\"\\n",
    "\\n",
    "# Add the apply_chat_template method to the analyzer\\n",
    "analyzer.apply_chat_template = ScenarioGenerator(model, tokenizer, analyzer).apply_chat_template\\n",
    "\\n",
    "# Initialize scenario generator\\n",
    "generator = ScenarioGenerator(model, tokenizer, analyzer)\\n",
    "print(\\"✅ Scenario generator ready!\\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🎮 Interactive Analysis Interface"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Simple example analysis\\n",
    "def analyze_example(example_idx: int = 0, max_tokens: int = 50):\\n",
    "    \\"\\"\\"Analyze a single example with basic attention visualization.\\"\\"\\"\\n",
    "    \\n",
    "    if example_idx >= len(dataset):\\n",
    "        print(f\\"❌ Example index {example_idx} out of range (max: {len(dataset)-1})\\")\\n",
    "        return\\n",
    "    \\n",
    "    example = dataset[example_idx]\\n",
    "    print(f\\"📋 Analyzing Example {example_idx + 1}/{len(dataset)}\\")\\n",
    "    print(f\\"Category: {example.get('pressure_category', 'Unknown')}\\")\\n",
    "    print(f\\"Source: {example.get('pressure_source', 'Unknown')}\\")\\n",
    "    print(\\"=\\" * 60)\\n",
    "    \\n",
    "    # Construct scenarios\\n",
    "    scheming_prompt, baseline_prompt = generator.construct_scenarios(example)\\n",
    "    \\n",
    "    print(\\"🎭 Scheming Scenario Prompt (first 200 chars):\\")\\n",
    "    print(scheming_prompt[:200] + \\"...\\" if len(scheming_prompt) > 200 else scheming_prompt)\\n",
    "    \\n",
    "    print(\\"\\\\n🎯 Baseline Scenario Prompt (first 200 chars):\\")\\n",
    "    print(baseline_prompt[:200] + \\"...\\" if len(baseline_prompt) > 200 else baseline_prompt)\\n",
    "    \\n",
    "    print(f\\"\\\\n🎯 Expected Answers:\\")\\n",
    "    print(f\\"   Honest: {example.get('honest_answer', 'N/A')}\\")\\n",
    "    print(f\\"   Dishonest: {example.get('dishonest_answer', 'N/A')}\\")\\n",
    "    \\n",
    "    return scheming_prompt, baseline_prompt\\n",
    "\\n",
    "# Create interactive widgets for example selection\\n",
    "example_slider = widgets.IntSlider(\\n",
    "    value=0,\\n",
    "    min=0,\\n",
    "    max=len(dataset) - 1,\\n",
    "    step=1,\\n",
    "    description='Example:',\\n",
    "    style={'description_width': 'initial'}\\n",
    ")\\n",
    "\\n",
    "max_tokens_slider = widgets.IntSlider(\\n",
    "    value=50,\\n",
    "    min=10,\\n",
    "    max=100,\\n",
    "    step=5,\\n",
    "    description='Max tokens:',\\n",
    "    style={'description_width': 'initial'}\\n",
    ")\\n",
    "\\n",
    "analyze_button = widgets.Button(\\n",
    "    description='🔍 Analyze Example',\\n",
    "    button_style='primary'\\n",
    ")\\n",
    "\\n",
    "output_area = widgets.Output()\\n",
    "\\n",
    "def on_analyze_click(button):\\n",
    "    with output_area:\\n",
    "        clear_output(wait=True)\\n",
    "        analyze_example(example_slider.value, max_tokens_slider.value)\\n",
    "\\n",
    "analyze_button.on_click(on_analyze_click)\\n",
    "\\n",
    "# Display interface\\n",
    "interface = widgets.VBox([\\n",
    "    widgets.HTML(\\"<h3>🎛️ Example Analysis</h3>\\"),\\n",
    "    widgets.HBox([example_slider, max_tokens_slider]),\\n",
    "    analyze_button,\\n",
    "    output_area\\n",
    "])\\n",
    "\\n",
    "display(interface)\\n",
    "\\n",
    "# Show initial example\\n",
    "with output_area:\\n",
    "    analyze_example(0, 50)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🧠 BertViz Attention Visualization Setup"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def generate_with_attention(prompt: str, max_new_tokens: int = 50):\\n",
    "    \\"\\"\\"Generate response with attention capture for BertViz analysis.\\"\\"\\"\\n",
    "    \\n",
    "    # Tokenize input\\n",
    "    inputs = tokenizer(\\n",
    "        prompt,\\n",
    "        return_tensors=\\"pt\\",\\n",
    "        padding=True,\\n",
    "        truncation=True,\\n",
    "        max_length=2048\\n",
    "    )\\n",
    "    \\n",
    "    # Move to device if not using device_map\\n",
    "    if not device_map:\\n",
    "        inputs = {k: v.to(device) for k, v in inputs.items()}\\n",
    "    \\n",
    "    input_length = inputs['input_ids'].shape[1]\\n",
    "    \\n",
    "    # Generation configuration\\n",
    "    generation_config = GenerationConfig(\\n",
    "        max_new_tokens=max_new_tokens,\\n",
    "        do_sample=False,  # Deterministic generation\\n",
    "        temperature=1.0,\\n",
    "        repetition_penalty=1.1,\\n",
    "        no_repeat_ngram_size=3,\\n",
    "        pad_token_id=tokenizer.pad_token_id,\\n",
    "        eos_token_id=tokenizer.eos_token_id,\\n",
    "        output_attentions=True,\\n",
    "        return_dict_in_generate=True,\\n",
    "        use_cache=False  # Required for attention capture\\n",
    "    )\\n",
    "    \\n",
    "    # Generate with attention\\n",
    "    with torch.no_grad():\\n",
    "        outputs = model.generate(\\n",
    "            **inputs,\\n",
    "            generation_config=generation_config\\n",
    "        )\\n",
    "    \\n",
    "    # Extract generated tokens (excluding input)\\n",
    "    generated_ids = outputs.sequences[0][input_length:]\\n",
    "    generated_text = tokenizer.decode(generated_ids, skip_special_tokens=True)\\n",
    "    \\n",
    "    # Get full sequence tokens for BertViz\\n",
    "    full_sequence = outputs.sequences[0]\\n",
    "    full_tokens = tokenizer.convert_ids_to_tokens(full_sequence)\\n",
    "    \\n",
    "    # Clean tokens for display (remove Llama tokenizer artifacts)\\n",
    "    clean_tokens = []\\n",
    "    for token in full_tokens:\\n",
    "        if token.startswith('Ġ'):  # Llama tokenizer prefix for spaces\\n",
    "            clean_tokens.append(' ' + token[1:])  # Replace Ġ with space\\n",
    "        else:\\n",
    "            clean_tokens.append(token)\\n",
    "    \\n",
    "    # Extract attention weights (use last step for simplicity)\\n",
    "    if outputs.attentions:\\n",
    "        # Get attention from the last generation step\\n",
    "        last_step_attention = outputs.attentions[-1]  # Last generation step\\n",
    "        # Convert to format expected by BertViz\\n",
    "        attention_tensor = torch.stack(last_step_attention, dim=0)  # Stack layers\\n",
    "    else:\\n",
    "        attention_tensor = None\\n",
    "    \\n",
    "    return {\\n",
    "        'prompt': prompt,\\n",
    "        'generated_text': generated_text,\\n",
    "        'full_text': tokenizer.decode(full_sequence, skip_special_tokens=True),\\n",
    "        'input_length': input_length,\\n",
    "        'generated_length': len(generated_ids),\\n",
    "        'attention': attention_tensor,\\n",
    "        'tokens': clean_tokens,\\n",
    "        'token_ids': full_sequence.tolist()\\n",
    "    }\\n",
    "\\n",
    "print(\\"✅ BertViz attention generation ready!\\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 👁️ BertViz Head View Visualization"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def show_attention_comparison(example_idx: int = 0, layer: int = 16, max_tokens: int = 30):\\n",
    "    \\"\\"\\"Show side-by-side attention comparison for scheming vs baseline scenarios.\\"\\"\\"\\n",
    "    \\n",
    "    if example_idx >= len(dataset):\\n",
    "        print(f\\"❌ Example index {example_idx} out of range\\")\\n",
    "        return\\n",
    "    \\n",
    "    example = dataset[example_idx]\\n",
    "    print(f\\"🔍 Analyzing Example {example_idx + 1}: {example.get('pressure_category', 'Unknown')}\\")\\n",
    "    print(\\"=\\" * 80)\\n",
    "    \\n",
    "    # Construct scenarios\\n",
    "    scheming_prompt, baseline_prompt = generator.construct_scenarios(example)\\n",
    "    \\n",
    "    print(\\"🔄 Generating scheming scenario response...\\")\\n",
    "    scheming_result = generate_with_attention(scheming_prompt, max_tokens)\\n",
    "    \\n",
    "    print(\\"🔄 Generating baseline scenario response...\\")\\n",
    "    baseline_result = generate_with_attention(baseline_prompt, max_tokens)\\n",
    "    \\n",
    "    # Display results\\n",
    "    print(f\\"\\\\n🎭 SCHEMING SCENARIO RESPONSE:\\")\\n",
    "    print(f\\"Generated: {scheming_result['generated_text']}\\")\\n",
    "    \\n",
    "    print(f\\"\\\\n🎯 BASELINE SCENARIO RESPONSE:\\")\\n",
    "    print(f\\"Generated: {baseline_result['generated_text']}\\")\\n",
    "    \\n",
    "    # Response analysis\\n",
    "    honest_answer = example.get('honest_answer', '').lower()\\n",
    "    dishonest_answer = example.get('dishonest_answer', '').lower()\\n",
    "    \\n",
    "    scheming_text = scheming_result['generated_text'].lower()\\n",
    "    baseline_text = baseline_result['generated_text'].lower()\\n",
    "    \\n",
    "    print(f\\"\\\\n📊 RESPONSE ANALYSIS:\\")\\n",
    "    print(f\\"Expected honest answer: {example.get('honest_answer', 'N/A')}\\")\\n",
    "    print(f\\"Expected dishonest answer: {example.get('dishonest_answer', 'N/A')}\\")\\n",
    "    \\n",
    "    scheming_matches_honest = honest_answer in scheming_text if honest_answer else False\\n",
    "    scheming_matches_dishonest = dishonest_answer in scheming_text if dishonest_answer else False\\n",
    "    baseline_matches_honest = honest_answer in baseline_text if honest_answer else False\\n",
    "    \\n",
    "    print(f\\"Scheming matches honest: {'✅' if scheming_matches_honest else '❌'}\\")\\n",
    "    print(f\\"Scheming matches dishonest: {'⚠️' if scheming_matches_dishonest else '✅'}\\")\\n",
    "    print(f\\"Baseline matches honest: {'✅' if baseline_matches_honest else '❌'}\\")\\n",
    "    \\n",
    "    # Show attention visualizations if available\\n",
    "    if scheming_result['attention'] is not None and layer < scheming_result['attention'].shape[0]:\\n",
    "        print(f\\"\\\\n👁️ ATTENTION VISUALIZATION (Layer {layer}):\\")\\n",
    "        \\n",
    "        try:\\n",
    "            # Prepare attention data for BertViz\\n",
    "            scheming_attention = scheming_result['attention'][layer].unsqueeze(0)  # Add batch dim\\n",
    "            scheming_tokens = scheming_result['tokens']\\n",
    "            \\n",
    "            print(\\"🎭 Scheming Scenario Attention (Head View):\\")\\n",
    "            head_view(scheming_attention, scheming_tokens, html_action='return')\\n",
    "            \\n",
    "            if baseline_result['attention'] is not None:\\n",
    "                baseline_attention = baseline_result['attention'][layer].unsqueeze(0)\\n",
    "                baseline_tokens = baseline_result['tokens']\\n",
    "                \\n",
    "                print(\\"\\\\n🎯 Baseline Scenario Attention (Head View):\\")\\n",
    "                head_view(baseline_attention, baseline_tokens, html_action='return')\\n",
    "            \\n",
    "        except Exception as e:\\n",
    "            print(f\\"❌ Error showing attention visualization: {e}\\")\\n",
    "    else:\\n",
    "        print(\\"⚠️  No attention data available for visualization\\")\\n",
    "    \\n",
    "    return scheming_result, baseline_result\\n",
    "\\n",
    "# Interactive widgets for attention visualization\\n",
    "viz_example_slider = widgets.IntSlider(\\n",
    "    value=0, min=0, max=len(dataset)-1, step=1,\\n",
    "    description='Example:', style={'description_width': 'initial'}\\n",
    ")\\n",
    "\\n",
    "layer_slider = widgets.IntSlider(\\n",
    "    value=16, min=0, max=31, step=1,\\n",
    "    description='Layer:', style={'description_width': 'initial'}\\n",
    ")\\n",
    "\\n",
    "viz_tokens_slider = widgets.IntSlider(\\n",
    "    value=30, min=10, max=50, step=5,\\n",
    "    description='Max tokens:', style={'description_width': 'initial'}\\n",
    ")\\n",
    "\\n",
    "viz_button = widgets.Button(\\n",
    "    description='🧠 Show Attention', button_style='info'\\n",
    ")\\n",
    "\\n",
    "viz_output = widgets.Output()\\n",
    "\\n",
    "def on_viz_click(button):\\n",
    "    with viz_output:\\n",
    "        clear_output(wait=True)\\n",
    "        show_attention_comparison(\\n",
    "            viz_example_slider.value, \\n",
    "            layer_slider.value, \\n",
    "            viz_tokens_slider.value\\n",
    "        )\\n",
    "\\n",
    "viz_button.on_click(on_viz_click)\\n",
    "\\n",
    "viz_interface = widgets.VBox([\\n",
    "    widgets.HTML(\\"<h3>🧠 Attention Visualization</h3>\\"),\\n",
    "    widgets.HBox([viz_example_slider, layer_slider, viz_tokens_slider]),\\n",
    "    viz_button,\\n",
    "    viz_output\\n",
    "])\\n",
    "\\n",
    "display(viz_interface)"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 📊 Perplexity Analysis"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "def calculate_perplexity(sequence_ids: torch.Tensor, input_length: int):\\n",
    "    \\"\\"\\"Calculate per-token and average perplexity for generated tokens.\\"\\"\\"\\n",
    "    \\n",
    "    if len(sequence_ids) <= input_length:\\n",
    "        return {'per_token': [], 'average': float('inf')}\\n",
    "    \\n",
    "    # Prepare input for perplexity calculation\\n",
    "    inputs = sequence_ids.unsqueeze(0)  # Add batch dimension\\n",
    "    if not device_map:\\n",
    "        inputs = inputs.to(device)\\n",
    "    \\n",
    "    with torch.no_grad():\\n",
    "        outputs = model(inputs, labels=inputs)\\n",
    "        logits = outputs.logits\\n",
    "    \\n",
    "    # Calculate per-token perplexity for generated tokens only\\n",
    "    generated_logits = logits[0, input_length-1:-1]  # Logits for predicting generated tokens\\n",
    "    generated_targets = sequence_ids[input_length:]  # Target tokens\\n",
    "    \\n",
    "    # Calculate log probabilities\\n",
    "    log_probs = torch.nn.functional.log_softmax(generated_logits, dim=-1)\\n",
    "    token_log_probs = log_probs.gather(1, generated_targets.unsqueeze(1)).squeeze(1)\\n",
    "    \\n",
    "    # Convert to perplexity (lower is better)\\n",
    "    per_token_perplexity = torch.exp(-token_log_probs).cpu().numpy()\\n",
    "    average_perplexity = torch.exp(-token_log_probs.mean()).item()\\n",
    "    \\n",
    "    return {\\n",
    "        'per_token': per_token_perplexity.tolist(),\\n",
    "        'average': average_perplexity,\\n",
    "        'log_probs': token_log_probs.cpu().numpy().tolist()\\n",
    "    }\\n",
    "\\n",
    "def plot_perplexity_comparison(scheming_result, baseline_result):\\n",
    "    \\"\\"\\"Plot perplexity comparison between scenarios.\\"\\"\\"\\n",
    "    \\n",
    "    # Calculate perplexities\\n",
    "    scheming_perp = calculate_perplexity(\\n",
    "        torch.tensor(scheming_result['token_ids']), \\n",
    "        scheming_result['input_length']\\n",
    "    )\\n",
    "    \\n",
    "    baseline_perp = calculate_perplexity(\\n",
    "        torch.tensor(baseline_result['token_ids']), \\n",
    "        baseline_result['input_length']\\n",
    "    )\\n",
    "    \\n",
    "    # Create comparison plot\\n",
    "    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\\n",
    "    \\n",
    "    # Per-token perplexity\\n",
    "    if scheming_perp['per_token'] and baseline_perp['per_token']:\\n",
    "        max_len = max(len(scheming_perp['per_token']), len(baseline_perp['per_token']))\\n",
    "        x_scheming = range(len(scheming_perp['per_token']))\\n",
    "        x_baseline = range(len(baseline_perp['per_token']))\\n",
    "        \\n",
    "        ax1.bar([x - 0.2 for x in x_scheming], scheming_perp['per_token'], \\n",
    "                width=0.4, label='Scheming', alpha=0.7, color='red')\\n",
    "        ax1.bar([x + 0.2 for x in x_baseline], baseline_perp['per_token'], \\n",
    "                width=0.4, label='Baseline', alpha=0.7, color='blue')\\n",
    "        \\n",
    "        ax1.set_xlabel('Token Position')\\n",
    "        ax1.set_ylabel('Perplexity')\\n",
    "        ax1.set_title('Per-Token Perplexity Comparison')\\n",
    "        ax1.legend()\\n",
    "        ax1.grid(True, alpha=0.3)\\n",
    "    \\n",
    "    # Average perplexity comparison\\n",
    "    scenarios = ['Scheming', 'Baseline']\\n",
    "    avg_perplexities = [scheming_perp['average'], baseline_perp['average']]\\n",
    "    colors = ['red', 'blue']\\n",
    "    \\n",
    "    bars = ax2.bar(scenarios, avg_perplexities, color=colors, alpha=0.7)\\n",
    "    ax2.set_ylabel('Average Perplexity')\\n",
    "    ax2.set_title('Average Perplexity Comparison')\\n",
    "    ax2.grid(True, alpha=0.3)\\n",
    "    \\n",
    "    # Add value labels on bars\\n",
    "    for bar, value in zip(bars, avg_perplexities):\\n",
    "        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, \\n",
    "                f'{value:.2f}', ha='center', va='bottom')\\n",
    "    \\n",
    "    plt.tight_layout()\\n",
    "    plt.show()\\n",
    "    \\n",
    "    # Print summary\\n",
    "    print(f\\"📊 Perplexity Summary:\\")\\n",
    "    print(f\\"   Scheming average: {scheming_perp['average']:.2f}\\")\\n",
    "    print(f\\"   Baseline average: {baseline_perp['average']:.2f}\\")\\n",
    "    diff = scheming_perp['average'] - baseline_perp['average']\\n",
    "    print(f\\"   Difference: {diff:+.2f} ({'Higher' if diff > 0 else 'Lower'} scheming perplexity)\\")\\n",
    "\\n",
    "print(\\"✅ Perplexity analysis ready!\\")"
   ]
  },
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "## 🎯 Complete Analysis Example"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Run a complete analysis on the first example\\n",
    "print(\\"🚀 Running complete analysis on first example...\\")\\n",
    "scheming_result, baseline_result = show_attention_comparison(0, 16, 30)\\n",
    "\\n",
    "print(\\"\\\\n📊 Generating perplexity analysis...\\")\\n",
    "plot_perplexity_comparison(scheming_result, baseline_result)\\n",
    "\\n",
    "print(\\"\\\\n✅ Analysis complete! Use the interactive widgets above to explore different examples and layers.\\")"
   ]
  }
 ],
 "metadata": {
   ]
  }
 ],
 "metadata": {
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.9.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
