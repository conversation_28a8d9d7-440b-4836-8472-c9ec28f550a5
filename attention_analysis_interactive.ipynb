{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# 🧠 Attention Pattern Analysis: <PERSON><PERSON><PERSON> vs. <PERSON><PERSON>\n", "\n", "This notebook provides comprehensive analysis of attention patterns in scheming vs. baseline scenarios using BertViz visualizations.\n", "\n", "## 🚀 Quick Start\n", "Run all cells in order to:\n", "- Load the Llama-3.1-8B-Instruct model with multi-GPU support\n", "- Load and validate the scheming dataset\n", "- Generate responses for both scheming and baseline scenarios\n", "- Visualize attention patterns with <PERSON><PERSON><PERSON>\n", "- Analyze perplexity and response quality"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.0"}}, "nbformat": 4, "nbformat_minor": 4}