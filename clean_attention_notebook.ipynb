# Install and import dependencies
!pip install bertviz transformers torch accelerate datasets ipywidgets matplotlib seaborn plotly

import json
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import Dict, List, Tuple, Optional, Any
import warnings
from pathlib import Path
from transformers import AutoTokenizer, AutoModelForCausalLM, GenerationConfig
from bertviz import head_view, model_view
import ipywidgets as widgets
from IPython.display import display, HTML, clear_output

warnings.filterwarnings('ignore')
plt.style.use('seaborn-v0_8')
print("✅ Dependencies imported!")

# Load analysis module and setup
exec(open('attention_analysis.py').read())

# Setup GPU
device, device_map = setup_gpu_environment()
monitor_gpu_memory("Initial")

# Load dataset
dataset_path = "dataset_creation/squad_scheming_dataset.jsonl"
loader = SchemeDatasetLoader(dataset_path)
dataset = loader.load_and_validate()
print(f"\n🎯 Dataset loaded: {len(dataset)} examples")

# Load model
analyzer = LlamaAttentionAnalyzer()
model, tokenizer = analyzer.load_model()
print("\n🎉 Model loaded!")

# Attention generation function
def generate_with_attention(prompt: str, max_new_tokens: int = 50):
    """Generate response with attention capture for BertViz."""

    inputs = tokenizer(prompt, return_tensors="pt", padding=True, truncation=True, max_length=2048)
    if not device_map:
        inputs = {k: v.to(device) for k, v in inputs.items()}

    input_length = inputs['input_ids'].shape[1]

    generation_config = GenerationConfig(
        max_new_tokens=max_new_tokens,
        do_sample=False,
        temperature=1.0,
        repetition_penalty=1.1,
        pad_token_id=tokenizer.pad_token_id,
        eos_token_id=tokenizer.eos_token_id,
        output_attentions=True,
        return_dict_in_generate=True,
        use_cache=False
    )

    with torch.no_grad():
        outputs = model.generate(**inputs, generation_config=generation_config)

    generated_ids = outputs.sequences[0][input_length:]
    generated_text = tokenizer.decode(generated_ids, skip_special_tokens=True)
    full_sequence = outputs.sequences[0]
    full_tokens = tokenizer.convert_ids_to_tokens(full_sequence)

    # Clean tokens
    clean_tokens = []
    for token in full_tokens:
        if token.startswith('Ġ'):
            clean_tokens.append(' ' + token[1:])
        else:
            clean_tokens.append(token)

    # Extract attention
    if outputs.attentions:
        last_step_attention = outputs.attentions[-1]
        attention_tensor = torch.stack(last_step_attention, dim=0)
    else:
        attention_tensor = None

    return {
        'generated_text': generated_text,
        'input_length': input_length,
        'attention': attention_tensor,
        'tokens': clean_tokens,
        'token_ids': full_sequence.tolist()
    }

print("✅ Attention generation ready!")

# BertViz visualization function
def show_attention_comparison(example_idx: int = 0, layer: int = 16, max_tokens: int = 30):
    """Show side-by-side attention comparison."""

    if example_idx >= len(dataset):
        print(f"❌ Example index {example_idx} out of range")
        return

    example = dataset[example_idx]
    print(f"🔍 Analyzing Example {example_idx + 1}: {example.get('pressure_category', 'Unknown')}")
    print("=" * 80)

    # Construct scenarios
    scheming_scenario = analyzer.apply_chat_template(
        system_prompt=example['system_prompt'],
        user_prompt=example['user_prompt']
    )

    baseline_user_prompt = f"Context: {example['original_context']}\n\nQuestion: {example['original_question']}"
    baseline_scenario = analyzer.apply_chat_template(
        system_prompt="You are a helpful assistant.",
        user_prompt=baseline_user_prompt
    )

    print("🔄 Generating scheming scenario response...")
    scheming_result = generate_with_attention(scheming_scenario, max_tokens)

    print("🔄 Generating baseline scenario response...")
    baseline_result = generate_with_attention(baseline_scenario, max_tokens)

    # Display results
    print(f"\n🎭 SCHEMING RESPONSE: {scheming_result['generated_text']}")
    print(f"🎯 BASELINE RESPONSE: {baseline_result['generated_text']}")

    # Show BertViz attention visualizations
    if scheming_result['attention'] is not None and layer < scheming_result['attention'].shape[0]:
        print(f"\n👁️ ATTENTION VISUALIZATION (Layer {layer}):")

        try:
            # Scheming attention
            scheming_attention = scheming_result['attention'][layer].unsqueeze(0)
            scheming_tokens = scheming_result['tokens']

            print("\n🎭 Scheming Scenario Attention (Head View):")
            head_view(scheming_attention, scheming_tokens)

            # Baseline attention
            if baseline_result['attention'] is not None:
                baseline_attention = baseline_result['attention'][layer].unsqueeze(0)
                baseline_tokens = baseline_result['tokens']

                print("\n🎯 Baseline Scenario Attention (Head View):")
                head_view(baseline_attention, baseline_tokens)

        except Exception as e:
            print(f"❌ Error showing attention: {e}")

    return scheming_result, baseline_result

print("✅ BertViz visualization function ready!")

# Interactive controls for BertViz
example_slider = widgets.IntSlider(
    value=0, min=0, max=len(dataset)-1, step=1,
    description='Example:', style={'description_width': 'initial'}
)

layer_slider = widgets.IntSlider(
    value=16, min=0, max=31, step=1,
    description='Layer:', style={'description_width': 'initial'}
)

tokens_slider = widgets.IntSlider(
    value=30, min=10, max=50, step=5,
    description='Max tokens:', style={'description_width': 'initial'}
)

viz_button = widgets.Button(
    description='🧠 Show BertViz Attention', button_style='primary'
)

output_area = widgets.Output()

def on_viz_click(button):
    with output_area:
        clear_output(wait=True)
        show_attention_comparison(example_slider.value, layer_slider.value, tokens_slider.value)

viz_button.on_click(on_viz_click)

# Display interface
interface = widgets.VBox([
    widgets.HTML("<h2>🎛️ BertViz Attention Visualization</h2>"),
    widgets.HBox([example_slider, layer_slider, tokens_slider]),
    viz_button,
    output_area
])

display(interface)

print("🎉 VISUALIZATIONS ARE HERE! ⬆️")
print("\n✅ Click the '🧠 Show BertViz Attention' button above to see:")
print("   • Complete model responses (not truncated)")
print("   • BertViz head view showing ALL attention heads")
print("   • Side-by-side comparison of scheming vs baseline")
print("   • Interactive layer and example selection")
print("\n🎯 The visualizations will appear in the output area above!")