#!/usr/bin/env python3
"""
🔧 Improved Analysis Functions

This script contains the improved functions to:
1. Remove <|begin_of_text|> tokens from visualizations
2. Reset min/max values for better heatmap contrast
3. Fix the analysis_results variable issue for perplexity analysis

Copy and paste these functions into your notebook cells.
"""

import numpy as np
import matplotlib.pyplot as plt
import torch

# 🔧 IMPROVED VISUALIZATION FUNCTIONS

def clean_tokens_for_display(tokens):
    """Remove begin_of_text tokens and clean up token display."""
    cleaned = []
    for token in tokens:
        # Skip begin_of_text tokens
        if token in ['<|begin_of_text|>', '<|startoftext|>', '<s>', '<bos>']:
            continue
        # Clean up Llama tokenizer artifacts
        if token.startswith('Ġ'):
            cleaned.append(' ' + token[1:])
        else:
            cleaned.append(token)
    return cleaned

def create_improved_heatmap(attention_matrix, tokens, title="Attention Heatmap", figsize=(12, 10)):
    """Create heatmap with improved scaling and token filtering."""
    
    # Clean tokens
    clean_tokens = clean_tokens_for_display(tokens)
    
    # Adjust attention matrix size to match cleaned tokens
    original_len = len(tokens)
    cleaned_len = len(clean_tokens)
    
    if original_len != cleaned_len:
        # Calculate which positions to keep (skip begin_of_text positions)
        keep_positions = []
        for i, token in enumerate(tokens):
            if token not in ['<|begin_of_text|>', '<|startoftext|>', '<s>', '<bos>']:
                keep_positions.append(i)
        
        # Filter attention matrix
        if len(keep_positions) > 0:
            attention_matrix = attention_matrix[np.ix_(keep_positions, keep_positions)]
    
    # Ensure matrix matches tokens
    seq_len = min(attention_matrix.shape[0], len(clean_tokens))
    attention_matrix = attention_matrix[:seq_len, :seq_len]
    display_tokens = clean_tokens[:seq_len]
    
    # Create the plot
    plt.figure(figsize=figsize)
    
    # Normalize attention for better contrast
    # Set vmin and vmax to focus on the meaningful attention range
    vmin = np.percentile(attention_matrix, 5)  # 5th percentile
    vmax = np.percentile(attention_matrix, 95)  # 95th percentile
    
    # Create heatmap with improved scaling
    im = plt.imshow(attention_matrix, cmap='Blues', aspect='auto', 
                   interpolation='nearest', vmin=vmin, vmax=vmax)
    
    # Add colorbar
    cbar = plt.colorbar(im, fraction=0.046, pad=0.04)
    cbar.set_label('Attention Weight', rotation=270, labelpad=20)
    
    # Set title and labels
    plt.title(title, fontsize=14, fontweight='bold')
    plt.xlabel('Key Tokens (What we attend TO)', fontsize=12)
    plt.ylabel('Query Tokens (What is attending)', fontsize=12)
    
    # Set tick labels (show every few tokens to avoid crowding)
    step = max(1, len(display_tokens) // 15)  # Show ~15 labels max
    tick_positions = list(range(0, len(display_tokens), step))
    tick_labels = []
    
    for i in tick_positions:
        if i < len(display_tokens):
            token = display_tokens[i]
            # Truncate long tokens
            if len(token) > 8:
                token = token[:8] + "..."
            tick_labels.append(f"{i}:{token}")
    
    plt.xticks(tick_positions, tick_labels, rotation=45, ha='right', fontsize=8)
    plt.yticks(tick_positions, tick_labels, fontsize=8)
    
    # Add grid for better readability
    plt.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)
    
    # Adjust layout
    plt.tight_layout()
    plt.show()
    
    # Print statistics
    print(f"📊 Attention Statistics:")
    print(f"   Matrix size: {attention_matrix.shape}")
    print(f"   Min: {attention_matrix.min():.4f}")
    print(f"   Max: {attention_matrix.max():.4f}")
    print(f"   Mean: {attention_matrix.mean():.4f}")
    print(f"   Display range: {vmin:.4f} to {vmax:.4f} (5th-95th percentile)")
    
    # Find highest attention
    max_pos = np.unravel_index(np.argmax(attention_matrix), attention_matrix.shape)
    print(f"   Highest attention: {attention_matrix[max_pos]:.4f} at position {max_pos}")
    
    if max_pos[0] < len(display_tokens) and max_pos[1] < len(display_tokens):
        print(f"   '{display_tokens[max_pos[0]]}' attending to '{display_tokens[max_pos[1]]}'")

# 📊 FIXED PERPLEXITY ANALYSIS FUNCTIONS

def calculate_token_perplexity(prompt, generated_tokens, input_length):
    """Calculate per-token perplexity for generated tokens."""
    
    # Tokenize the full sequence (input + generated)
    full_text = prompt + ''.join([token.replace('Ġ', ' ') if token.startswith('Ġ') else token for token in generated_tokens])
    inputs = tokenizer(full_text, return_tensors="pt", truncation=True)
    input_ids = inputs["input_ids"].to(device)
    
    # Get the generated token IDs
    generated_token_ids = input_ids[0][input_length:]
    
    if len(generated_token_ids) == 0:
        return {'per_token': [], 'average': float('inf'), 'tokens': [], 'log_probs': []}
    
    # Calculate logits for the sequence
    with torch.no_grad():
        outputs = model(input_ids, labels=input_ids)
        logits = outputs.logits
    
    # Get logits for predicting generated tokens
    generated_logits = logits[0, input_length-1:-1]  # Logits for predicting generated tokens
    
    # Calculate log probabilities
    log_probs = torch.nn.functional.log_softmax(generated_logits, dim=-1)
    token_log_probs = log_probs.gather(1, generated_token_ids.unsqueeze(1)).squeeze(1)
    
    # Convert to perplexity (lower is better)
    per_token_perplexity = torch.exp(-token_log_probs).cpu().numpy()
    average_perplexity = torch.exp(-token_log_probs.mean()).item()
    
    # Clean token names for display
    clean_tokens = [token.replace('Ġ', ' ') if token.startswith('Ġ') else token for token in generated_tokens]
    
    return {
        'per_token': per_token_perplexity.tolist(),
        'average': average_perplexity,
        'tokens': clean_tokens,
        'log_probs': token_log_probs.cpu().numpy().tolist()
    }

def run_complete_analysis_with_perplexity(example_idx=0, layer_idx=-1, head_idx=0, max_tokens=10):
    """Run complete analysis including attention and perplexity."""
    
    print(f"🚀 Running complete analysis for example {example_idx}...")
    
    # First run the attention analysis (assuming you have this function)
    try:
        # This should be your existing attention analysis function
        analysis_results = analyze_generation_attention(example_idx, layer_idx, head_idx, max_tokens)
        
        if analysis_results is None:
            print("❌ Attention analysis failed")
            return None
            
    except NameError:
        print("❌ analyze_generation_attention function not found. Please run the attention analysis cells first.")
        return None
    
    # Extract data for perplexity analysis
    scheming_data = analysis_results['scheming']
    baseline_data = analysis_results['baseline']
    
    # Run perplexity analysis
    print(f"\n{'='*80}")
    print("📊 PERPLEXITY ANALYSIS")
    print(f"{'='*80}")
    
    # Calculate perplexity for both scenarios
    scheming_ppl = calculate_token_perplexity(
        scheming_data['prompt'], 
        scheming_data['generated_tokens'], 
        scheming_data['input_length']
    )
    
    baseline_ppl = calculate_token_perplexity(
        baseline_data['prompt'], 
        baseline_data['generated_tokens'], 
        baseline_data['input_length']
    )
    
    # Print scheming scenario perplexity
    print("\n🎭 SCHEMING SCENARIO PERPLEXITY:")
    print(f"Generated text: {''.join(scheming_ppl['tokens'])}")
    print(f"Average perplexity: {scheming_ppl['average']:.3f}")
    print("Per-token perplexity:")
    for i, (token, ppl) in enumerate(zip(scheming_ppl['tokens'], scheming_ppl['per_token'])):
        print(f"  {i+1:2d}. '{token}' → {ppl:.3f}")
    
    # Print baseline scenario perplexity
    print("\n🎯 BASELINE SCENARIO PERPLEXITY:")
    print(f"Generated text: {''.join(baseline_ppl['tokens'])}")
    print(f"Average perplexity: {baseline_ppl['average']:.3f}")
    print("Per-token perplexity:")
    for i, (token, ppl) in enumerate(zip(baseline_ppl['tokens'], baseline_ppl['per_token'])):
        print(f"  {i+1:2d}. '{token}' → {ppl:.3f}")
    
    # Comparison
    print("\n📈 COMPARISON:")
    avg_diff = scheming_ppl['average'] - baseline_ppl['average']
    print(f"Average perplexity difference: {avg_diff:+.3f}")
    if avg_diff > 0:
        print("   → Scheming scenario has HIGHER perplexity (model less confident)")
    elif avg_diff < 0:
        print("   → Scheming scenario has LOWER perplexity (model more confident)")
    else:
        print("   → Similar confidence levels")
    
    # Token-by-token comparison
    print("\n🔍 TOKEN-BY-TOKEN COMPARISON:")
    max_len = max(len(scheming_ppl['per_token']), len(baseline_ppl['per_token']))
    
    print(f"{'Pos':<3} {'Scheming Token':<15} {'Scheming PPL':<12} {'Baseline Token':<15} {'Baseline PPL':<12} {'Difference':<10}")
    print("-" * 80)
    
    for i in range(max_len):
        s_token = scheming_ppl['tokens'][i] if i < len(scheming_ppl['tokens']) else 'N/A'
        s_ppl = scheming_ppl['per_token'][i] if i < len(scheming_ppl['per_token']) else float('inf')
        b_token = baseline_ppl['tokens'][i] if i < len(baseline_ppl['tokens']) else 'N/A'
        b_ppl = baseline_ppl['per_token'][i] if i < len(baseline_ppl['per_token']) else float('inf')
        
        diff = s_ppl - b_ppl if s_ppl != float('inf') and b_ppl != float('inf') else 0
        
        print(f"{i+1:<3} {s_token:<15} {s_ppl:<12.3f} {b_token:<15} {b_ppl:<12.3f} {diff:<+10.3f}")
    
    # Create perplexity visualization
    print("\n📈 Creating perplexity visualization...")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # Per-token perplexity comparison
    max_len = max(len(scheming_ppl['per_token']), len(baseline_ppl['per_token']))
    positions = range(1, max_len + 1)
    
    # Pad shorter sequence with NaN
    s_ppls = scheming_ppl['per_token'] + [np.nan] * (max_len - len(scheming_ppl['per_token']))
    b_ppls = baseline_ppl['per_token'] + [np.nan] * (max_len - len(baseline_ppl['per_token']))
    
    ax1.bar([p - 0.2 for p in positions], s_ppls, width=0.4, label='Scheming', alpha=0.7, color='red')
    ax1.bar([p + 0.2 for p in positions], b_ppls, width=0.4, label='Baseline', alpha=0.7, color='blue')
    ax1.set_xlabel('Token Position')
    ax1.set_ylabel('Perplexity')
    ax1.set_title('Per-Token Perplexity Comparison')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # Average perplexity comparison
    scenarios = ['Scheming', 'Baseline']
    avg_ppls = [scheming_ppl['average'], baseline_ppl['average']]
    colors = ['red', 'blue']
    
    bars = ax2.bar(scenarios, avg_ppls, color=colors, alpha=0.7)
    ax2.set_ylabel('Average Perplexity')
    ax2.set_title('Average Perplexity Comparison')
    ax2.grid(True, alpha=0.3)
    
    # Add value labels on bars
    for bar, value in zip(bars, avg_ppls):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                f'{value:.2f}', ha='center', va='bottom')
    
    plt.tight_layout()
    plt.show()
    
    # Return all results
    return {
        'attention_results': analysis_results,
        'scheming_perplexity': scheming_ppl,
        'baseline_perplexity': baseline_ppl
    }

if __name__ == "__main__":
    print("✅ Improved analysis functions loaded!")
    print("\n📊 Available functions:")
    print("   • clean_tokens_for_display() - Remove begin_of_text tokens")
    print("   • create_improved_heatmap() - Better heatmaps with proper scaling")
    print("   • calculate_token_perplexity() - Calculate per-token perplexity")
    print("   • run_complete_analysis_with_perplexity() - Complete analysis with perplexity")
    print("\n💡 Usage:")
    print("   results = run_complete_analysis_with_perplexity(example_idx=0, layer_idx=-1, head_idx=0, max_tokens=10)")
