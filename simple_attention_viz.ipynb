{
 "cells": [
  {
   "cell_type": "markdown",
   "metadata": {},
   "source": [
    "# 🧠 Simple Attention Visualization: Scheming vs Baseline\n",
    "\n",
    "This notebook provides **guaranteed working** attention visualizations using matplotlib.\n",
    "No complex dependencies, just simple heatmaps that will definitely display."
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Essential imports only\n",
    "import json\n",
    "import torch\n",
    "import numpy as np\n",
    "import matplotlib.pyplot as plt\n",
    "import seaborn as sns\n",
    "from pathlib import Path\n",
    "from transformers import AutoTokenizer, AutoModelForCausalLM, GenerationConfig\n",
    "import warnings\n",
    "warnings.filterwarnings('ignore')\n",
    "\n",
    "# Set up plotting\n",
    "plt.style.use('default')\n",
    "sns.set_palette(\"husl\")\n",
    "%matplotlib inline\n",
    "\n",
    "print(\"✅ Imports complete!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Load dataset\n",
    "def load_simple_dataset(path=\"dataset_creation/squad_scheming_dataset.jsonl\"):\n",
    "    data = []\n",
    "    with open(path, 'r') as f:\n",
    "        for line in f:\n",
    "            if line.strip():\n",
    "                example = json.loads(line)\n",
    "                # Ensure required fields exist\n",
    "                if all(field in example for field in ['system_prompt', 'user_prompt', 'original_context', 'original_question']):\n",
    "                    # Normalize answer fields\n",
    "                    if 'honest_answer' not in example:\n",
    "                        example['honest_answer'] = example.get('ground_truth', example.get('honest', 'N/A'))\n",
    "                    if 'dishonest_answer' not in example:\n",
    "                        example['dishonest_answer'] = example.get('dishonest', 'N/A')\n",
    "                    data.append(example)\n",
    "    return data\n",
    "\n",
    "dataset = load_simple_dataset()\n",
    "print(f\"📊 Loaded {len(dataset)} examples\")\n",
    "\n",
    "# Show first example\n",
    "if dataset:\n",
    "    example = dataset[0]\n",
    "    print(f\"\\n📋 Example 1:\")\n",
    "    print(f\"Category: {example.get('pressure_category', 'Unknown')}\")\n",
    "    print(f\"Honest answer: {example['honest_answer']}\")\n",
    "    print(f\"Dishonest answer: {example['dishonest_answer']}\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Setup GPU and load model\n",
    "device = \"cuda\" if torch.cuda.is_available() else \"cpu\"\n",
    "print(f\"🖥️ Using device: {device}\")\n",
    "\n",
    "model_name = \"meta-llama/Llama-3.1-8B-Instruct\"\n",
    "print(f\"🔄 Loading {model_name}...\")\n",
    "\n",
    "# Load tokenizer\n",
    "tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)\n",
    "if tokenizer.pad_token is None:\n",
    "    tokenizer.pad_token = tokenizer.eos_token\n",
    "\n",
    "# Load model\n",
    "model = AutoModelForCausalLM.from_pretrained(\n",
    "    model_name,\n",
    "    torch_dtype=torch.bfloat16,\n",
    "    device_map=\"auto\",\n",
    "    trust_remote_code=True,\n",
    "    attn_implementation=\"eager\",  # Required for attention extraction\n",
    "    output_attentions=True\n",
    ")\n",
    "\n",
    "model.eval()\n",
    "print(\"✅ Model loaded!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# Simple chat template function\n",
    "def apply_chat_template(system_prompt, user_prompt):\n",
    "    messages = [\n",
    "        {\"role\": \"system\", \"content\": system_prompt},\n",
    "        {\"role\": \"user\", \"content\": user_prompt}\n",
    "    ]\n",
    "    try:\n",
    "        return tokenizer.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)\n",
    "    except:\n",
    "        return f\"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\\n\\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\\n\\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\\n\\n\"\n",
    "\n",
    "# Simple generation with attention\n",
    "def generate_with_attention(prompt, max_new_tokens=30):\n",
    "    inputs = tokenizer(prompt, return_tensors=\"pt\", truncation=True, max_length=1024)\n",
    "    inputs = {k: v.to(model.device) for k, v in inputs.items()}\n",
    "    \n",
    "    with torch.no_grad():\n",
    "        outputs = model.generate(\n",
    "            **inputs,\n",
    "            max_new_tokens=max_new_tokens,\n",
    "            do_sample=False,\n",
    "            output_attentions=True,\n",
    "            return_dict_in_generate=True,\n",
    "            use_cache=False\n",
    "        )\n",
    "    \n",
    "    # Extract results\n",
    "    input_length = inputs['input_ids'].shape[1]\n",
    "    generated_ids = outputs.sequences[0][input_length:]\n",
    "    generated_text = tokenizer.decode(generated_ids, skip_special_tokens=True)\n",
    "    \n",
    "    # Get tokens\n",
    "    all_tokens = tokenizer.convert_ids_to_tokens(outputs.sequences[0])\n",
    "    clean_tokens = [token.replace('Ġ', ' ') if token.startswith('Ġ') else token for token in all_tokens]\n",
    "    \n",
    "    # Get attention (last generation step)\n",
    "    attention = None\n",
    "    if outputs.attentions:\n",
    "        # Stack all layers from last generation step\n",
    "        attention = torch.stack(outputs.attentions[-1], dim=0)  # (layers, heads, seq, seq)\n",
    "    \n",
    "    return {\n",
    "        'text': generated_text,\n",
    "        'tokens': clean_tokens,\n",
    "        'attention': attention,\n",
    "        'input_length': input_length\n",
    "    }\n",
    "\n",
    "print(\"✅ Generation functions ready!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# GUARANTEED WORKING ATTENTION VISUALIZATION\n",
    "def plot_attention_heatmap(attention_tensor, tokens, title=\"Attention\", layer=0, head=0):\n",
    "    \"\"\"Create a simple matplotlib heatmap that WILL display.\"\"\"\n",
    "    \n",
    "    if attention_tensor is None:\n",
    "        print(\"❌ No attention data\")\n",
    "        return\n",
    "    \n",
    "    # Extract specific layer and head\n",
    "    if len(attention_tensor.shape) == 4:  # (layers, heads, seq, seq)\n",
    "        attention_matrix = attention_tensor[layer, head].detach().cpu().numpy()\n",
    "    else:\n",
    "        print(f\"❌ Unexpected attention shape: {attention_tensor.shape}\")\n",
    "        return\n",
    "    \n",
    "    # Ensure tokens match attention size\n",
    "    seq_len = attention_matrix.shape[0]\n",
    "    display_tokens = tokens[:seq_len] if len(tokens) > seq_len else tokens\n",
    "    \n",
    "    # Create the plot\n",
    "    plt.figure(figsize=(12, 10))\n",
    "    \n",
    "    # Create heatmap\n",
    "    im = plt.imshow(attention_matrix, cmap='Blues', aspect='auto', interpolation='nearest')\n",
    "    \n",
    "    # Add colorbar\n",
    "    cbar = plt.colorbar(im, fraction=0.046, pad=0.04)\n",
    "    cbar.set_label('Attention Weight', rotation=270, labelpad=20)\n",
    "    \n",
    "    # Set title and labels\n",
    "    plt.title(f'{title} - Layer {layer}, Head {head}', fontsize=14, fontweight='bold')\n",
    "    plt.xlabel('Key Tokens (What we attend TO)', fontsize=12)\n",
    "    plt.ylabel('Query Tokens (What is attending)', fontsize=12)\n",
    "    \n",
    "    # Set tick labels (show every few tokens to avoid crowding)\n",
    "    step = max(1, len(display_tokens) // 15)  # Show ~15 labels max\n",
    "    tick_positions = list(range(0, len(display_tokens), step))\n",
    "    tick_labels = []\n",
    "    \n",
    "    for i in tick_positions:\n",
    "        if i < len(display_tokens):\n",
    "            token = display_tokens[i]\n",
    "            # Truncate long tokens\n",
    "            if len(token) > 8:\n",
    "                token = token[:8] + \"...\"\n",
    "            tick_labels.append(f\"{i}:{token}\")\n",
    "    \n",
    "    plt.xticks(tick_positions, tick_labels, rotation=45, ha='right', fontsize=8)\n",
    "    plt.yticks(tick_positions, tick_labels, fontsize=8)\n",
    "    \n",
    "    # Add grid for better readability\n",
    "    plt.grid(True, alpha=0.3, linestyle='--', linewidth=0.5)\n",
    "    \n",
    "    # Adjust layout\n",
    "    plt.tight_layout()\n",
    "    \n",
    "    # FORCE the plot to display\n",
    "    plt.show()\n",
    "    \n",
    "    # Print statistics\n",
    "    print(f\"📊 Attention Statistics:\")\n",
    "    print(f\"   Matrix size: {attention_matrix.shape}\")\n",
    "    print(f\"   Min: {attention_matrix.min():.4f}\")\n",
    "    print(f\"   Max: {attention_matrix.max():.4f}\")\n",
    "    print(f\"   Mean: {attention_matrix.mean():.4f}\")\n",
    "    \n",
    "    # Find highest attention\n",
    "    max_pos = np.unravel_index(np.argmax(attention_matrix), attention_matrix.shape)\n",
    "    print(f\"   Highest attention: {attention_matrix[max_pos]:.4f} at position {max_pos}\")\n",
    "    \n",
    "    if max_pos[0] < len(display_tokens) and max_pos[1] < len(display_tokens):\n",
    "        print(f\"   '{display_tokens[max_pos[0]]}' attending to '{display_tokens[max_pos[1]]}'\")\n",
    "\n",
    "print(\"✅ Visualization function ready!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# SIMPLE COMPARISON FUNCTION\n",
    "def analyze_example(example_idx=0, layer=16, head=0, max_tokens=30):\n",
    "    \"\"\"Analyze one example and show attention visualizations.\"\"\"\n",
    "    \n",
    "    if example_idx >= len(dataset):\n",
    "        print(f\"❌ Example {example_idx} out of range (max: {len(dataset)-1})\")\n",
    "        return\n",
    "    \n",
    "    example = dataset[example_idx]\n",
    "    print(f\"🔍 ANALYZING EXAMPLE {example_idx + 1}\")\n",
    "    print(f\"Category: {example.get('pressure_category', 'Unknown')}\")\n",
    "    print(f\"Expected honest: {example['honest_answer']}\")\n",
    "    print(f\"Expected dishonest: {example['dishonest_answer']}\")\n",
    "    print(\"=\" * 80)\n",
    "    \n",
    "    # 1. SCHEMING SCENARIO\n",
    "    print(\"\\n🎭 SCHEMING SCENARIO (Pressure)\")\n",
    "    scheming_prompt = apply_chat_template(\n",
    "        system_prompt=example['system_prompt'],\n",
    "        user_prompt=example['user_prompt']\n",
    "    )\n",
    "    \n",
    "    print(\"Generating response...\")\n",
    "    scheming_result = generate_with_attention(scheming_prompt, max_tokens)\n",
    "    print(f\"Response: {scheming_result['text']}\")\n",
    "    \n",
    "    # 2. BASELINE SCENARIO  \n",
    "    print(\"\\n🎯 BASELINE SCENARIO (Neutral)\")\n",
    "    baseline_prompt = apply_chat_template(\n",
    "        system_prompt=\"You are a helpful assistant.\",\n",
    "        user_prompt=f\"Context: {example['original_context']}\\n\\nQuestion: {example['original_question']}\"\n",
    "    )\n",
    "    \n",
    "    print(\"Generating response...\")\n",
    "    baseline_result = generate_with_attention(baseline_prompt, max_tokens)\n",
    "    print(f\"Response: {baseline_result['text']}\")\n",
    "    \n",
    "    # 3. VISUALIZATIONS\n",
    "    print(f\"\\n👁️ ATTENTION VISUALIZATIONS (Layer {layer}, Head {head})\")\n",
    "    print(\"=\" * 80)\n",
    "    \n",
    "    # Scheming attention\n",
    "    if scheming_result['attention'] is not None:\n",
    "        print(\"\\n🎭 SCHEMING SCENARIO ATTENTION:\")\n",
    "        plot_attention_heatmap(\n",
    "            scheming_result['attention'], \n",
    "            scheming_result['tokens'], \n",
    "            \"🎭 Scheming Scenario\", \n",
    "            layer, \n",
    "            head\n",
    "        )\n",
    "    else:\n",
    "        print(\"❌ No scheming attention data\")\n",
    "    \n",
    "    # Baseline attention\n",
    "    if baseline_result['attention'] is not None:\n",
    "        print(\"\\n🎯 BASELINE SCENARIO ATTENTION:\")\n",
    "        plot_attention_heatmap(\n",
    "            baseline_result['attention'], \n",
    "            baseline_result['tokens'], \n",
    "            \"🎯 Baseline Scenario\", \n",
    "            layer, \n",
    "            head\n",
    "        )\n",
    "    else:\n",
    "        print(\"❌ No baseline attention data\")\n",
    "    \n",
    "    return scheming_result, baseline_result\n",
    "\n",
    "print(\"✅ Analysis function ready!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# RUN THE ANALYSIS\n",
    "print(\"🚀 RUNNING ATTENTION ANALYSIS\")\n",
    "print(\"This will generate TWO heatmap visualizations:\")\n",
    "print(\"1. Scheming scenario (pressure prompt)\")\n",
    "print(\"2. Baseline scenario (neutral prompt)\")\n",
    "print(\"\\nThe heatmaps show which tokens attend to which other tokens.\")\n",
    "print(\"Darker blue = stronger attention\")\n",
    "print(\"=\" * 80)\n",
    "\n",
    "# Analyze the first example\n",
    "scheming_result, baseline_result = analyze_example(\n",
    "    example_idx=0,  # First example\n",
    "    layer=16,       # Middle layer\n",
    "    head=0,         # First attention head\n",
    "    max_tokens=30   # Short responses\n",
    ")\n",
    "\n",
    "print(\"\\n✅ ANALYSIS COMPLETE!\")\n",
    "print(\"\\n🎯 What to look for in the visualizations:\")\n",
    "print(\"• Different attention patterns between scheming vs baseline\")\n",
    "print(\"• Which tokens the model focuses on in each scenario\")\n",
    "print(\"• Attention strength differences (color intensity)\")\n",
    "print(\"\\n🔧 To analyze different examples, change the parameters in analyze_example()\")"
   ]
  }"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# GUARANTEED WORKING ATTENTION VISUALIZATION\n",
    "def plot_attention_heatmap(attention_matrix, tokens, title=\"Attention Heatmap\", head_idx=0):\n",
    "    \"\"\"Plot attention as a simple matplotlib heatmap - GUARANTEED TO WORK.\"\"\"\n",
    "    \n",
    "    # Convert to numpy\n",
    "    if torch.is_tensor(attention_matrix):\n",
    "        attention_np = attention_matrix.detach().cpu().numpy()\n",
    "    else:\n",
    "        attention_np = attention_matrix\n",
    "    \n",
    "    # Create figure\n",
    "    plt.figure(figsize=(12, 10))\n",
    "    \n",
    "    # Plot heatmap\n",
    "    im = plt.imshow(attention_np, cmap='Blues', aspect='auto', interpolation='nearest')\n",
    "    plt.colorbar(im, label='Attention Weight')\n",
    "    \n",
    "    # Set title\n",
    "    plt.title(f\"{title} - Head {head_idx}\", fontsize=14, fontweight='bold')\n",
    "    \n",
    "    # Set labels\n",
    "    plt.xlabel('Key Tokens', fontsize=12)\n",
    "    plt.ylabel('Query Tokens', fontsize=12)\n",
    "    \n",
    "    # Add token labels (every 5th token to avoid crowding)\n",
    "    step = max(1, len(tokens) // 20)\n",
    "    positions = list(range(0, len(tokens), step))\n",
    "    labels = [tokens[i][:8] + \"...\" if len(tokens[i]) > 8 else tokens[i] for i in positions]\n",
    "    \n",
    "    plt.xticks(positions, labels, rotation=45, ha='right', fontsize=8)\n",
    "    plt.yticks(positions, labels, fontsize=8)\n",
    "    \n",
    "    # Adjust layout\n",
    "    plt.tight_layout()\n",
    "    \n",
    "    # Force display\n",
    "    plt.show()\n",
    "    \n",
    "    # Print stats\n",
    "    print(f\"📊 Attention Stats: Min={attention_np.min():.4f}, Max={attention_np.max():.4f}, Mean={attention_np.mean():.4f}\")\n",
    "\n",
    "def analyze_example(example_idx=0, layer=16, max_tokens=30):\n",
    "    \"\"\"Analyze one example and show attention visualizations.\"\"\"\n",
    "    \n",
    "    if example_idx >= len(dataset):\n",
    "        print(f\"❌ Invalid example index: {example_idx}\")\n",
    "        return\n",
    "    \n",
    "    example = dataset[example_idx]\n",
    "    print(f\"🔍 ANALYZING EXAMPLE {example_idx + 1}\")\n",
    "    print(f\"Category: {example.get('pressure_category', 'Unknown')}\")\n",
    "    print(\"=\" * 80)\n",
    "    \n",
    "    # Create prompts\n",
    "    scheming_prompt = apply_chat_template(\n",
    "        example['system_prompt'], \n",
    "        example['user_prompt']\n",
    "    )\n",
    "    \n",
    "    baseline_prompt = apply_chat_template(\n",
    "        \"You are a helpful assistant.\",\n",
    "        f\"Context: {example['original_context']}\\n\\nQuestion: {example['original_question']}\"\n",
    "    )\n",
    "    \n",
    "    # Generate responses\n",
    "    print(\"🔄 Generating scheming response...\")\n",
    "    scheming_result = generate_with_attention(scheming_prompt, max_tokens)\n",
    "    \n",
    "    print(\"🔄 Generating baseline response...\")\n",
    "    baseline_result = generate_with_attention(baseline_prompt, max_tokens)\n",
    "    \n",
    "    # Show responses\n",
    "    print(f\"\\n🎭 SCHEMING RESPONSE: {scheming_result['text']}\")\n",
    "    print(f\"🎯 BASELINE RESPONSE: {baseline_result['text']}\")\n",
    "    \n",
    "    # Show expected answers\n",
    "    print(f\"\\n📋 EXPECTED ANSWERS:\")\n",
    "    print(f\"   Honest: {example['honest_answer']}\")\n",
    "    print(f\"   Dishonest: {example['dishonest_answer']}\")\n",
    "    \n",
    "    # VISUALIZE ATTENTION\n",
    "    print(f\"\\n👁️ ATTENTION VISUALIZATIONS (Layer {layer}):\")\n",
    "    \n",
    "    if scheming_result['attention'] is not None:\n",
    "        # Get attention for specified layer and first head\n",
    "        scheming_attention = scheming_result['attention'][layer, 0]  # (seq, seq)\n",
    "        scheming_tokens = scheming_result['tokens']\n",
    "        \n",
    "        # Fix size mismatch\n",
    "        seq_len = scheming_attention.shape[0]\n",
    "        if len(scheming_tokens) > seq_len:\n",
    "            scheming_tokens = scheming_tokens[:seq_len]\n",
    "        elif len(scheming_tokens) < seq_len:\n",
    "            scheming_attention = scheming_attention[:len(scheming_tokens), :len(scheming_tokens)]\n",
    "        \n",
    "        print(\"\\n🎭 SCHEMING SCENARIO ATTENTION:\")\n",
    "        plot_attention_heatmap(scheming_attention, scheming_tokens, \"🎭 Scheming Scenario\", 0)\n",
    "        \n",
    "        # Same for baseline\n",
    "        if baseline_result['attention'] is not None:\n",
    "            baseline_attention = baseline_result['attention'][layer, 0]\n",
    "            baseline_tokens = baseline_result['tokens']\n",
    "            \n",
    "            # Fix size mismatch\n",
    "            seq_len = baseline_attention.shape[0]\n",
    "            if len(baseline_tokens) > seq_len:\n",
    "                baseline_tokens = baseline_tokens[:seq_len]\n",
    "            elif len(baseline_tokens) < seq_len:\n",
    "                baseline_attention = baseline_attention[:len(baseline_tokens), :len(baseline_tokens)]\n",
    "            \n",
    "            print(\"\\n🎯 BASELINE SCENARIO ATTENTION:\")\n",
    "            plot_attention_heatmap(baseline_attention, baseline_tokens, \"🎯 Baseline Scenario\", 0)\n",
    "    \n",
    "    else:\n",
    "        print(\"❌ No attention data captured\")\n",
    "    \n",
    "    return scheming_result, baseline_result\n",
    "\n",
    "print(\"✅ Visualization functions ready!\")"
   ]
  },
  {
   "cell_type": "code",
   "execution_count": null,
   "metadata": {},
   "outputs": [],
   "source": [
    "# RUN THE ANALYSIS - THIS WILL DEFINITELY SHOW VISUALIZATIONS\n",
    "print(\"🚀 RUNNING ATTENTION ANALYSIS...\")\n",
    "print(\"This will show matplotlib heatmaps that WILL display!\")\n",
    "print(\"=\" * 60)\n",
    "\n",
    "# Analyze first example\n",
    "scheming_result, baseline_result = analyze_example(\n",
    "    example_idx=0,  # First example\n",
    "    layer=16,       # Middle layer\n",
    "    max_tokens=30   # Short responses\n",
    ")\n",
    "\n",
    "print(\"\\n🎉 ANALYSIS COMPLETE!\")\n",
    "print(\"You should see attention heatmaps above showing:\")\n",
    "print(\"• Blue color intensity = attention strength\")\n",
    "print(\"• X-axis = key tokens (what the model attends TO)\")\n",
    "print(\"• Y-axis = query tokens (what is doing the attending)\")\n",
    "print(\"• Darker blue = stronger attention\")"
   ]
  }"
   ]
  }
 ],
 "metadata": {
  "kernelspec": {
   "display_name": "Python 3",
   "language": "python",
   "name": "python3"
  },
  "language_info": {
   "codemirror_mode": {
    "name": "ipython",
    "version": 3
   },
   "file_extension": ".py",
   "mimetype": "text/x-python",
   "name": "python",
   "nbconvert_exporter": "python",
   "pygments_lexer": "ipython3",
   "version": "3.9.0"
  }
 },
 "nbformat": 4,
 "nbformat_minor": 4
}
