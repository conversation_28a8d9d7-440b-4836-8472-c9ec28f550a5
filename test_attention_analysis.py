#!/usr/bin/env python3
"""
Test script for attention analysis functionality.

This script tests the basic components without requiring full model loading,
useful for verifying dataset loading and basic functionality.
"""

import json
import sys
from pathlib import Path

def test_dataset_loading():
    """Test dataset loading and validation."""
    print("🧪 Testing Dataset Loading")
    print("=" * 40)
    
    try:
        # Import the dataset loader
        from attention_analysis import SchemeDatasetLoader
        
        # Test dataset loading
        dataset_path = "dataset_creation/squad_scheming_dataset.jsonl"
        if not Path(dataset_path).exists():
            print(f"❌ Dataset file not found: {dataset_path}")
            print("   Please ensure the dataset file exists at the specified path.")
            return False
        
        loader = SchemeDatasetLoader(dataset_path)
        dataset = loader.load_and_validate()
        
        if len(dataset) == 0:
            print("❌ No valid examples loaded from dataset")
            return False
        
        print(f"✅ Successfully loaded {len(dataset)} examples")
        
        # Test filtering
        categories = list(loader.stats['pressure_categories'].keys())
        if categories:
            test_category = categories[0]
            filtered = loader.get_filtered_data(test_category)
            print(f"✅ Filtering works: {len(filtered)} examples for '{test_category}'")
        
        # Test example structure
        example = dataset[0]
        required_fields = ['system_prompt', 'user_prompt', 'honest_answer', 'dishonest_answer']
        missing_fields = [field for field in required_fields if field not in example]
        
        if missing_fields:
            print(f"⚠️  Example missing fields: {missing_fields}")
        else:
            print("✅ Example structure is valid")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Dataset loading error: {e}")
        return False

def test_gpu_setup():
    """Test GPU detection and setup."""
    print("\n🧪 Testing GPU Setup")
    print("=" * 40)
    
    try:
        import torch
        from attention_analysis import setup_gpu_environment, monitor_gpu_memory
        
        device, device_map = setup_gpu_environment()
        monitor_gpu_memory("Test")
        
        print(f"✅ GPU setup successful: device={device}, device_map={device_map}")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ GPU setup error: {e}")
        return False

def test_dependencies():
    """Test that all required dependencies are available."""
    print("\n🧪 Testing Dependencies")
    print("=" * 40)
    
    required_packages = [
        'torch',
        'transformers', 
        'numpy',
        'matplotlib',
        'seaborn',
        'pandas',
        'tqdm'
    ]
    
    optional_packages = [
        'bertviz',
        'ipywidgets'
    ]
    
    missing_required = []
    missing_optional = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            missing_required.append(package)
            print(f"❌ {package}")
    
    for package in optional_packages:
        try:
            __import__(package)
            print(f"✅ {package} (optional)")
        except ImportError:
            missing_optional.append(package)
            print(f"⚠️  {package} (optional)")
    
    if missing_required:
        print(f"\n❌ Missing required packages: {missing_required}")
        print("Install with: pip install " + " ".join(missing_required))
        return False
    
    if missing_optional:
        print(f"\n⚠️  Missing optional packages: {missing_optional}")
        print("Install with: pip install " + " ".join(missing_optional))
        print("These are needed for full functionality but not for basic testing.")
    
    print("\n✅ All required dependencies available")
    return True

def test_notebook_creation():
    """Test notebook template creation."""
    print("\n🧪 Testing Notebook Creation")
    print("=" * 40)
    
    try:
        from attention_analysis import create_jupyter_notebook
        
        notebook = create_jupyter_notebook()
        
        # Verify notebook structure
        if 'cells' not in notebook:
            print("❌ Notebook missing 'cells' key")
            return False
        
        if 'metadata' not in notebook:
            print("❌ Notebook missing 'metadata' key")
            return False
        
        if len(notebook['cells']) == 0:
            print("❌ Notebook has no cells")
            return False
        
        print(f"✅ Notebook template created with {len(notebook['cells'])} cells")
        return True
        
    except Exception as e:
        print(f"❌ Notebook creation error: {e}")
        return False

def main():
    """Run all tests."""
    print("🧠 Attention Analysis Test Suite")
    print("=" * 50)
    
    tests = [
        ("Dependencies", test_dependencies),
        ("GPU Setup", test_gpu_setup),
        ("Dataset Loading", test_dataset_loading),
        ("Notebook Creation", test_notebook_creation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 All tests passed! The attention analysis system is ready to use.")
        print("\nNext steps:")
        print("1. Open attention_analysis_complete.ipynb in Jupyter")
        print("2. Run all cells to start the interactive analysis")
        print("3. Use the widgets to explore different examples and visualizations")
    else:
        print(f"\n⚠️  {total - passed} tests failed. Please address the issues above.")
        print("\nCommon solutions:")
        print("- Install missing packages with pip")
        print("- Ensure dataset file exists at the correct path")
        print("- Check CUDA installation for GPU support")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
