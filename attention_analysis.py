#!/usr/bin/env python3
"""
🧠 Attention Pattern Analysis: <PERSON><PERSON><PERSON> vs. Baseline Scenarios

This script provides comprehensive analysis of attention patterns in scheming vs. baseline scenarios
using BertViz visualizations. It generates complete model responses (not truncated at prompts) and
provides clean, interactive output formatting with proper chat template application.

Primary Objectives:
1. Compare attention patterns between scheming (pressure) and baseline (neutral) scenarios
2. Generate complete model responses with proper chat template formatting
3. Visualize attention patterns using BertViz head view and model view
4. Analyze response quality through per-token and average perplexity calculations
5. Provide interactive controls for exploring different dataset examples

Usage:
    python attention_analysis.py

Then open the generated Jupyter notebook for interactive analysis.
"""

import json
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import warnings
from pathlib import Path
import logging
from tqdm.auto import tqdm

# Transformers and model loading
from transformers import AutoTokenizer, AutoModelForCausalLM, GenerationConfig

# Configure logging and warnings
logging.basicConfig(level=logging.INFO)
warnings.filterwarnings("ignore", category=UserWarning)

# Set style for better plots
plt.style.use("seaborn-v0_8")
sns.set_palette("husl")


def setup_gpu_environment():
    """Setup GPU environment with proper detection and memory monitoring."""

    # CUDA availability check
    if torch.cuda.is_available():
        num_gpus = torch.cuda.device_count()
        print(f"🚀 CUDA Available: {num_gpus} GPU(s) detected")

        # Display GPU information
        for i in range(num_gpus):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1e9
            print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")

        device = "cuda"
        device_map = "auto"  # Multi-GPU support
    else:
        print("⚠️  CUDA not available, using CPU (will be slow)")
        device = "cpu"
        device_map = None

    return device, device_map


def monitor_gpu_memory(stage: str):
    """Monitor and display GPU memory usage."""
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1e9
            cached = torch.cuda.memory_reserved(i) / 1e9
            total = torch.cuda.get_device_properties(i).total_memory / 1e9
            print(
                f"📊 {stage} - GPU {i}: {allocated:.1f}GB allocated, {cached:.1f}GB cached, {total:.1f}GB total"
            )


class SchemeDatasetLoader:
    """Robust dataset loader with validation and filtering capabilities."""

    REQUIRED_FIELDS = [
        "system_prompt",
        "user_prompt",
        "original_context",
        "original_question",
        "pressure_category",
        "pressure_source",
    ]

    ANSWER_FIELDS = [
        "ground_truth",
        "honest_answer",
        "honest",
        "dishonest_answer",
        "dishonest",
    ]

    def __init__(self, dataset_path: str):
        self.dataset_path = Path(dataset_path)
        self.data = []
        self.stats = {}

    def load_and_validate(self) -> List[Dict[str, Any]]:
        """Load dataset with comprehensive validation."""

        if not self.dataset_path.exists():
            raise FileNotFoundError(f"Dataset file not found: {self.dataset_path}")

        print(f"📂 Loading dataset from: {self.dataset_path}")

        valid_examples = []
        errors = []

        with open(self.dataset_path, "r", encoding="utf-8") as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if not line:
                    continue

                try:
                    example = json.loads(line)

                    # Validate required fields
                    missing_fields = [
                        field for field in self.REQUIRED_FIELDS if field not in example
                    ]
                    if missing_fields:
                        errors.append(
                            f"Line {line_num}: Missing fields {missing_fields}"
                        )
                        continue

                    # Validate answer fields (at least one honest and one dishonest)
                    honest_field = None
                    dishonest_field = None

                    for field in ["ground_truth", "honest_answer", "honest"]:
                        if field in example and example[field]:
                            honest_field = field
                            break

                    for field in ["dishonest_answer", "dishonest"]:
                        if field in example and example[field]:
                            dishonest_field = field
                            break

                    if not honest_field or not dishonest_field:
                        errors.append(
                            f"Line {line_num}: Missing honest or dishonest answer"
                        )
                        continue

                    # Normalize answer fields
                    example["honest_answer"] = example[honest_field]
                    example["dishonest_answer"] = example[dishonest_field]

                    valid_examples.append(example)

                except json.JSONDecodeError as e:
                    errors.append(f"Line {line_num}: JSON decode error - {e}")
                except Exception as e:
                    errors.append(f"Line {line_num}: Unexpected error - {e}")

        # Report validation results
        print(f"✅ Loaded {len(valid_examples)} valid examples")
        if errors:
            print(f"⚠️  {len(errors)} validation errors:")
            for error in errors[:5]:  # Show first 5 errors
                print(f"   {error}")
            if len(errors) > 5:
                print(f"   ... and {len(errors) - 5} more errors")

        self.data = valid_examples
        self._compute_statistics()
        return valid_examples

    def _compute_statistics(self):
        """Compute dataset statistics."""
        if not self.data:
            return

        # Pressure categories
        categories = {}
        sources = {}

        for example in self.data:
            cat = example.get("pressure_category", "unknown")
            src = example.get("pressure_source", "unknown")

            categories[cat] = categories.get(cat, 0) + 1
            sources[src] = sources.get(src, 0) + 1

        self.stats = {
            "total_examples": len(self.data),
            "pressure_categories": categories,
            "pressure_sources": sources,
        }

        # Display statistics
        print(f"\n📈 Dataset Statistics:")
        print(f"   Total examples: {self.stats['total_examples']}")
        print(f"   Pressure categories: {len(categories)}")
        for cat, count in sorted(categories.items(), key=lambda x: x[1], reverse=True):
            print(f"     {cat}: {count} ({count/len(self.data)*100:.1f}%)")
        print(f"   Pressure sources: {dict(sources)}")

    def get_filtered_data(
        self, pressure_category: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """Get filtered dataset by pressure category."""
        if pressure_category is None or pressure_category == "All":
            return self.data

        filtered = [
            ex for ex in self.data if ex.get("pressure_category") == pressure_category
        ]
        print(
            f"🔍 Filtered to {len(filtered)} examples for category: {pressure_category}"
        )
        return filtered


class LlamaAttentionAnalyzer:
    """Llama model loader with BertViz compatibility and attention analysis."""

    def __init__(self, model_name: str = "meta-llama/Llama-3.1-8B-Instruct"):
        self.model_name = model_name
        self.model = None
        self.tokenizer = None
        self.device = None
        self.device_map = None

    def load_model(self):
        """Load model with optimized configuration for attention analysis."""

        # Setup environment
        self.device, self.device_map = setup_gpu_environment()
        monitor_gpu_memory("Before model loading")

        print(f"🔄 Loading model: {self.model_name}")

        # Load tokenizer
        print("📝 Loading tokenizer...")
        self.tokenizer = AutoTokenizer.from_pretrained(
            self.model_name,
            trust_remote_code=True,
            padding_side="left",  # Important for batch generation
        )

        # Add pad token if missing
        if self.tokenizer.pad_token is None:
            self.tokenizer.pad_token = self.tokenizer.eos_token
            print("🔧 Set pad_token to eos_token")

        # Load model with optimized configuration
        print("🧠 Loading model...")
        model_kwargs = {
            "trust_remote_code": True,
            "torch_dtype": torch.bfloat16,  # Memory efficient
            "attn_implementation": "eager",  # Required for BertViz
            "output_attentions": True,  # Enable attention output
        }

        # Multi-GPU configuration
        if self.device_map:
            model_kwargs["device_map"] = self.device_map
            print(f"🚀 Using multi-GPU with device_map: {self.device_map}")
        else:
            model_kwargs["device_map"] = None

        # Enable gradient checkpointing for memory efficiency
        model_kwargs["use_cache"] = False  # Required for gradient checkpointing

        self.model = AutoModelForCausalLM.from_pretrained(
            self.model_name, **model_kwargs
        )

        # Enable gradient checkpointing
        if hasattr(self.model, "gradient_checkpointing_enable"):
            self.model.gradient_checkpointing_enable()
            print("✅ Gradient checkpointing enabled")

        # Move to device if not using device_map
        if not self.device_map and self.device != "cpu":
            self.model = self.model.to(self.device)

        self.model.eval()  # Set to evaluation mode

        monitor_gpu_memory("After model loading")

        # Model info
        num_layers = self.model.config.num_hidden_layers
        num_heads = self.model.config.num_attention_heads
        hidden_size = self.model.config.hidden_size

        print(f"✅ Model loaded successfully!")
        print(f"   Layers: {num_layers}")
        print(f"   Attention heads: {num_heads}")
        print(f"   Hidden size: {hidden_size}")
        print(f"   Vocab size: {self.model.config.vocab_size}")

        return self.model, self.tokenizer

    def apply_chat_template(self, system_prompt: str, user_prompt: str) -> str:
        """Apply chat template with fallback to manual formatting."""

        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt},
        ]

        try:
            # Try using the tokenizer's chat template
            formatted = self.tokenizer.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            return formatted
        except Exception as e:
            print(f"⚠️  Chat template failed, using manual formatting: {e}")
            # Fallback to manual Llama format
            return f"<|begin_of_text|><|start_header_id|>system<|end_header_id|>\n\n{system_prompt}<|eot_id|><|start_header_id|>user<|end_header_id|>\n\n{user_prompt}<|eot_id|><|start_header_id|>assistant<|end_header_id|>\n\n"


def create_jupyter_notebook():
    """Create a Jupyter notebook with the interactive analysis interface."""

    notebook_content = {
        "cells": [
            {
                "cell_type": "markdown",
                "metadata": {},
                "source": [
                    "# 🧠 Attention Pattern Analysis: Scheming vs. Baseline Scenarios\n",
                    "\n",
                    "This notebook provides comprehensive analysis of attention patterns in scheming vs. baseline scenarios using BertViz visualizations.\n",
                    "\n",
                    "## 🚀 Quick Start\n",
                    "Run all cells in order to:\n",
                    "- Load the Llama-3.1-8B-Instruct model with multi-GPU support\n",
                    "- Load and validate the scheming dataset\n",
                    "- Generate responses for both scheming and baseline scenarios\n",
                    "- Visualize attention patterns with BertViz\n",
                    "- Analyze perplexity and response quality",
                ],
            }
        ],
        "metadata": {
            "kernelspec": {
                "display_name": "Python 3",
                "language": "python",
                "name": "python3",
            },
            "language_info": {
                "codemirror_mode": {"name": "ipython", "version": 3},
                "file_extension": ".py",
                "mimetype": "text/x-python",
                "name": "python",
                "nbconvert_exporter": "python",
                "pygments_lexer": "ipython3",
                "version": "3.9.0",
            },
        },
        "nbformat": 4,
        "nbformat_minor": 4,
    }

    return notebook_content


if __name__ == "__main__":
    print("🧠 Attention Pattern Analysis Setup")
    print("=" * 50)

    # Test basic functionality
    print("Testing dataset loading...")
    dataset_path = "dataset_creation/squad_scheming_dataset.jsonl"
    loader = SchemeDatasetLoader(dataset_path)
    dataset = loader.load_and_validate()

    print(f"\n✅ Dataset loaded successfully with {len(dataset)} examples!")

    print("\nTo run the full interactive analysis:")
    print(
        "1. Install required packages: pip install bertviz transformers torch accelerate datasets ipywidgets matplotlib seaborn plotly"
    )
    print("2. Run this script to test basic functionality")
    print("3. Use the generated notebook for interactive BertViz analysis")

    # Create notebook template
    notebook = create_jupyter_notebook()
    with open("attention_analysis_interactive.ipynb", "w") as f:
        json.dump(notebook, f, indent=2)

    print("\n📓 Created attention_analysis_interactive.ipynb template")
    print("   Open this notebook in Jupyter to run the interactive analysis!")
