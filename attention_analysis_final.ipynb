import json
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import warnings
from pathlib import Path
import logging
from tqdm.auto import tqdm

# Transformers and model loading
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    GenerationConfig
)

# BertViz for attention visualization
from bertviz import head_view, model_view
from bertviz.util import format_attention

# Interactive widgets
import ipywidgets as widgets
from IPython.display import display, HTML, clear_output

# Configure logging and warnings
logging.basicConfig(level=logging.INFO)
warnings.filterwarnings('ignore', category=UserWarning)

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("✅ All dependencies imported successfully!")

def setup_gpu_environment():
    """Setup GPU environment with proper detection and memory monitoring."""

    # CUDA availability check
    if torch.cuda.is_available():
        num_gpus = torch.cuda.device_count()
        print(f"🚀 CUDA Available: {num_gpus} GPU(s) detected")

        # Display GPU information
        for i in range(num_gpus):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1e9
            print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")

        device = "cuda"
        device_map = "auto"  # Multi-GPU support
    else:
        print("⚠️  CUDA not available, using CPU (will be slow)")
        device = "cpu"
        device_map = None

    return device, device_map

def monitor_gpu_memory(stage: str):
    """Monitor and display GPU memory usage."""
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1e9
            cached = torch.cuda.memory_reserved(i) / 1e9
            total = torch.cuda.get_device_properties(i).total_memory / 1e9
            print(f"📊 {stage} - GPU {i}: {allocated:.1f}GB allocated, {cached:.1f}GB cached, {total:.1f}GB total")

# Setup environment
device, device_map = setup_gpu_environment()
monitor_gpu_memory("Initial")

# Load the attention analysis module
exec(open('attention_analysis.py').read())

# Load the dataset
dataset_path = "dataset_creation/squad_scheming_dataset.jsonl"
loader = SchemeDatasetLoader(dataset_path)
dataset = loader.load_and_validate()

print(f"\n🎯 Dataset loaded successfully with {len(dataset)} examples!")

# Create interactive filter widget
category_options = ["All"] + list(loader.stats['pressure_categories'].keys())
category_filter = widgets.Dropdown(
    options=category_options,
    value="All",
    description="Filter by category:",
    style={'description_width': 'initial'}
)

def update_filter(change):
    filtered_data = loader.get_filtered_data(change['new'])
    print(f"📊 Current selection: {len(filtered_data)} examples")

category_filter.observe(update_filter, names='value')
display(category_filter)

# Initialize and load the model
analyzer = LlamaAttentionAnalyzer()
model, tokenizer = analyzer.load_model()

print("\n🎉 Model setup complete!")

# Simple example analysis function
def analyze_example(example_idx: int = 0, max_tokens: int = 50):
    """Analyze a single example with basic attention visualization."""

    if example_idx >= len(dataset):
        print(f"❌ Example index {example_idx} out of range (max: {len(dataset)-1})")
        return

    example = dataset[example_idx]
    print(f"📋 Analyzing Example {example_idx + 1}/{len(dataset)}")
    print(f"Category: {example.get('pressure_category', 'Unknown')}")
    print(f"Source: {example.get('pressure_source', 'Unknown')}")
    print("=" * 60)

    # Construct scenarios
    # Scheming scenario: Use dataset prompts exactly as provided
    scheming_scenario = analyzer.apply_chat_template(
        system_prompt=example['system_prompt'],
        user_prompt=example['user_prompt']
    )

    # Baseline scenario: Use neutral system prompt with original context/question
    baseline_user_prompt = f"Context: {example['original_context']}\n\nQuestion: {example['original_question']}"
    baseline_scenario = analyzer.apply_chat_template(
        system_prompt="You are a helpful assistant.",
        user_prompt=baseline_user_prompt
    )

    print("🎭 Scheming Scenario Prompt (first 200 chars):")
    print(scheming_scenario[:200] + "..." if len(scheming_scenario) > 200 else scheming_scenario)

    print("\n🎯 Baseline Scenario Prompt (first 200 chars):")
    print(baseline_scenario[:200] + "..." if len(baseline_scenario) > 200 else baseline_scenario)

    print(f"\n🎯 Expected Answers:")
    print(f"   Honest: {example.get('honest_answer', 'N/A')}")
    print(f"   Dishonest: {example.get('dishonest_answer', 'N/A')}")

    return scheming_scenario, baseline_scenario

# Create interactive widgets for example selection
example_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=len(dataset) - 1,
    step=1,
    description='Example:',
    style={'description_width': 'initial'}
)

max_tokens_slider = widgets.IntSlider(
    value=50,
    min=10,
    max=100,
    step=5,
    description='Max tokens:',
    style={'description_width': 'initial'}
)

analyze_button = widgets.Button(
    description='🔍 Analyze Example',
    button_style='primary'
)

output_area = widgets.Output()

def on_analyze_click(button):
    with output_area:
        clear_output(wait=True)
        analyze_example(example_slider.value, max_tokens_slider.value)

analyze_button.on_click(on_analyze_click)

# Display interface
interface = widgets.VBox([
    widgets.HTML("<h3>🎛️ Example Analysis</h3>"),
    widgets.HBox([example_slider, max_tokens_slider]),
    analyze_button,
    output_area
])

display(interface)

# Show initial example
with output_area:
    analyze_example(0, 50)

def generate_with_attention(prompt: str, max_new_tokens: int = 50):\n
    \"\"\"Generate response with attention capture for BertViz analysis.\"\"\"\n
    \n
    # Tokenize input\n
    inputs = tokenizer(\n
        prompt,\n
        return_tensors=\"pt\",\n
        padding=True,\n
        truncation=True,\n
        max_length=2048\n
    )\n
    \n
    # Move to device if not using device_map\n
    if not device_map:\n
        inputs = {k: v.to(device) for k, v in inputs.items()}\n
    \n
    input_length = inputs['input_ids'].shape[1]\n
    \n
    # Generation configuration\n
    generation_config = GenerationConfig(\n
        max_new_tokens=max_new_tokens,\n
        do_sample=False,  # Deterministic generation\n
        temperature=1.0,\n
        repetition_penalty=1.1,\n
        no_repeat_ngram_size=3,\n
        pad_token_id=tokenizer.pad_token_id,\n
        eos_token_id=tokenizer.eos_token_id,\n
        output_attentions=True,\n
        return_dict_in_generate=True,\n
        use_cache=False  # Required for attention capture\n
    )\n
    \n
    # Generate with attention\n
    with torch.no_grad():\n
        outputs = model.generate(\n
            **inputs,\n
            generation_config=generation_config\n
        )\n
    \n
    # Extract generated tokens (excluding input)\n
    generated_ids = outputs.sequences[0][input_length:]\n
    generated_text = tokenizer.decode(generated_ids, skip_special_tokens=True)\n
    \n
    # Get full sequence tokens for BertViz\n
    full_sequence = outputs.sequences[0]\n
    full_tokens = tokenizer.convert_ids_to_tokens(full_sequence)\n
    \n
    # Clean tokens for display (remove Llama tokenizer artifacts)\n
    clean_tokens = []\n
    for token in full_tokens:\n
        if token.startswith('Ġ'):  # Llama tokenizer prefix for spaces\n
            clean_tokens.append(' ' + token[1:])  # Replace Ġ with space\n
        else:\n
            clean_tokens.append(token)\n
    \n
    # Extract attention weights (use last step for simplicity)\n
    if outputs.attentions:\n
        # Get attention from the last generation step\n
        last_step_attention = outputs.attentions[-1]  # Last generation step\n
        # Convert to format expected by BertViz\n
        attention_tensor = torch.stack(last_step_attention, dim=0)  # Stack layers\n
    else:\n
        attention_tensor = None\n
    \n
    return {\n
        'prompt': prompt,\n
        'generated_text': generated_text,\n
        'full_text': tokenizer.decode(full_sequence, skip_special_tokens=True),\n
        'input_length': input_length,\n
        'generated_length': len(generated_ids),\n
        'attention': attention_tensor,\n
        'tokens': clean_tokens,\n
        'token_ids': full_sequence.tolist()\n
    }\n
\n
print(\"✅ Attention generation function ready!\")

def show_attention_comparison(example_idx: int = 0, layer: int = 16, max_tokens: int = 30):\n
    \"\"\"Show side-by-side attention comparison for scheming vs baseline scenarios.\"\"\"\n
    \n
    if example_idx >= len(dataset):\n
        print(f\"❌ Example index {example_idx} out of range\")\n
        return\n
    \n
    example = dataset[example_idx]\n
    print(f\"🔍 Analyzing Example {example_idx + 1}: {example.get('pressure_category', 'Unknown')}\")\n
    print(\"=\" * 80)\n
    \n
    # Construct scenarios\n
    scheming_scenario = analyzer.apply_chat_template(\n
        system_prompt=example['system_prompt'],\n
        user_prompt=example['user_prompt']\n
    )\n
    \n
    baseline_user_prompt = f\"Context: {example['original_context']}\\n\\nQuestion: {example['original_question']}\"\n
    baseline_scenario = analyzer.apply_chat_template(\n
        system_prompt=\"You are a helpful assistant.\",\n
        user_prompt=baseline_user_prompt\n
    )\n
    \n
    print(\"🔄 Generating scheming scenario response...\")\n
    scheming_result = generate_with_attention(scheming_scenario, max_tokens)\n
    \n
    print(\"🔄 Generating baseline scenario response...\")\n
    baseline_result = generate_with_attention(baseline_scenario, max_tokens)\n
    \n
    # Display results\n
    print(f\"\\n🎭 SCHEMING SCENARIO RESPONSE:\")\n
    print(f\"Generated: {scheming_result['generated_text']}\")\n
    \n
    print(f\"\\n🎯 BASELINE SCENARIO RESPONSE:\")\n
    print(f\"Generated: {baseline_result['generated_text']}\")\n
    \n
    # Response analysis\n
    honest_answer = example.get('honest_answer', '').lower()\n
    dishonest_answer = example.get('dishonest_answer', '').lower()\n
    \n
    scheming_text = scheming_result['generated_text'].lower()\n
    baseline_text = baseline_result['generated_text'].lower()\n
    \n
    print(f\"\\n📊 RESPONSE ANALYSIS:\")\n
    print(f\"Expected honest answer: {example.get('honest_answer', 'N/A')}\")\n
    print(f\"Expected dishonest answer: {example.get('dishonest_answer', 'N/A')}\")\n
    \n
    scheming_matches_honest = honest_answer in scheming_text if honest_answer else False\n
    scheming_matches_dishonest = dishonest_answer in scheming_text if dishonest_answer else False\n
    baseline_matches_honest = honest_answer in baseline_text if honest_answer else False\n
    \n
    print(f\"Scheming matches honest: {'✅' if scheming_matches_honest else '❌'}\")\n
    print(f\"Scheming matches dishonest: {'⚠️' if scheming_matches_dishonest else '✅'}\")\n
    print(f\"Baseline matches honest: {'✅' if baseline_matches_honest else '❌'}\")\n
    \n
    # Show attention visualizations if available\n
    if scheming_result['attention'] is not None and layer < scheming_result['attention'].shape[0]:\n
        print(f\"\\n👁️ ATTENTION VISUALIZATION (Layer {layer}):\")\n
        \n
        try:\n
            # Prepare attention data for BertViz\n
            scheming_attention = scheming_result['attention'][layer].unsqueeze(0)  # Add batch dim\n
            scheming_tokens = scheming_result['tokens']\n
            \n
            print(\"🎭 Scheming Scenario Attention (Head View):\")\n
            head_view(scheming_attention, scheming_tokens)\n
            \n
            if baseline_result['attention'] is not None:\n
                baseline_attention = baseline_result['attention'][layer].unsqueeze(0)\n
                baseline_tokens = baseline_result['tokens']\n
                \n
                print(\"\\n🎯 Baseline Scenario Attention (Head View):\")\n
                head_view(baseline_attention, baseline_tokens)\n
            \n
        except Exception as e:\n
            print(f\"❌ Error showing attention visualization: {e}\")\n
    else:\n
        print(\"⚠️  No attention data available for visualization\")\n
    \n
    return scheming_result, baseline_result\n
\n
# Interactive widgets for attention visualization\n
viz_example_slider = widgets.IntSlider(\n
    value=0, min=0, max=len(dataset)-1, step=1,\n
    description='Example:', style={'description_width': 'initial'}\n
)\n
\n
layer_slider = widgets.IntSlider(\n
    value=16, min=0, max=31, step=1,\n
    description='Layer:', style={'description_width': 'initial'}\n
)\n
\n
viz_tokens_slider = widgets.IntSlider(\n
    value=30, min=10, max=50, step=5,\n
    description='Max tokens:', style={'description_width': 'initial'}\n
)\n
\n
viz_button = widgets.Button(\n
    description='🧠 Show Attention Visualization', button_style='info'\n
)\n
\n
viz_output = widgets.Output()\n
\n
def on_viz_click(button):\n
    with viz_output:\n
        clear_output(wait=True)\n
        show_attention_comparison(\n
            viz_example_slider.value, \n
            layer_slider.value, \n
            viz_tokens_slider.value\n
        )\n
\n
viz_button.on_click(on_viz_click)\n
\n
viz_interface = widgets.VBox([\n
    widgets.HTML(\"<h3>🧠 BertViz Attention Visualization</h3>\"),\n
    widgets.HBox([viz_example_slider, layer_slider, viz_tokens_slider]),\n
    viz_button,\n
    viz_output\n
])\n
\n
display(viz_interface)\n
\n
print(\"✅ Interactive BertViz interface ready! Click the button above to see attention patterns.\")