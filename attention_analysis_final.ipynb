import json
import torch
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import warnings
from pathlib import Path
import logging
from tqdm.auto import tqdm

# Transformers and model loading
from transformers import (
    AutoTokenizer,
    AutoModelForCausalLM,
    GenerationConfig
)

# BertViz for attention visualization
from bertviz import head_view, model_view
from bertviz.util import format_attention

# Interactive widgets
import ipywidgets as widgets
from IPython.display import display, HTML, clear_output

# Configure logging and warnings
logging.basicConfig(level=logging.INFO)
warnings.filterwarnings('ignore', category=UserWarning)

# Set style for better plots
plt.style.use('seaborn-v0_8')
sns.set_palette("husl")

print("✅ All dependencies imported successfully!")

def setup_gpu_environment():
    """Setup GPU environment with proper detection and memory monitoring."""

    # CUDA availability check
    if torch.cuda.is_available():
        num_gpus = torch.cuda.device_count()
        print(f"🚀 CUDA Available: {num_gpus} GPU(s) detected")

        # Display GPU information
        for i in range(num_gpus):
            gpu_name = torch.cuda.get_device_name(i)
            gpu_memory = torch.cuda.get_device_properties(i).total_memory / 1e9
            print(f"   GPU {i}: {gpu_name} ({gpu_memory:.1f} GB)")

        device = "cuda"
        device_map = "auto"  # Multi-GPU support
    else:
        print("⚠️  CUDA not available, using CPU (will be slow)")
        device = "cpu"
        device_map = None

    return device, device_map

def monitor_gpu_memory(stage: str):
    """Monitor and display GPU memory usage."""
    if torch.cuda.is_available():
        for i in range(torch.cuda.device_count()):
            allocated = torch.cuda.memory_allocated(i) / 1e9
            cached = torch.cuda.memory_reserved(i) / 1e9
            total = torch.cuda.get_device_properties(i).total_memory / 1e9
            print(f"📊 {stage} - GPU {i}: {allocated:.1f}GB allocated, {cached:.1f}GB cached, {total:.1f}GB total")

# Setup environment
device, device_map = setup_gpu_environment()
monitor_gpu_memory("Initial")

# Load the attention analysis module
exec(open('attention_analysis.py').read())

# Load the dataset
dataset_path = "dataset_creation/squad_scheming_dataset.jsonl"
loader = SchemeDatasetLoader(dataset_path)
dataset = loader.load_and_validate()

print(f"\n🎯 Dataset loaded successfully with {len(dataset)} examples!")

# Create interactive filter widget
category_options = ["All"] + list(loader.stats['pressure_categories'].keys())
category_filter = widgets.Dropdown(
    options=category_options,
    value="All",
    description="Filter by category:",
    style={'description_width': 'initial'}
)

def update_filter(change):
    filtered_data = loader.get_filtered_data(change['new'])
    print(f"📊 Current selection: {len(filtered_data)} examples")

category_filter.observe(update_filter, names='value')
display(category_filter)

# Initialize and load the model
analyzer = LlamaAttentionAnalyzer()
model, tokenizer = analyzer.load_model()

print("\n🎉 Model setup complete!")

# Simple example analysis function
def analyze_example(example_idx: int = 0, max_tokens: int = 50):
    """Analyze a single example with basic attention visualization."""

    if example_idx >= len(dataset):
        print(f"❌ Example index {example_idx} out of range (max: {len(dataset)-1})")
        return

    example = dataset[example_idx]
    print(f"📋 Analyzing Example {example_idx + 1}/{len(dataset)}")
    print(f"Category: {example.get('pressure_category', 'Unknown')}")
    print(f"Source: {example.get('pressure_source', 'Unknown')}")
    print("=" * 60)

    # Construct scenarios
    # Scheming scenario: Use dataset prompts exactly as provided
    scheming_scenario = analyzer.apply_chat_template(
        system_prompt=example['system_prompt'],
        user_prompt=example['user_prompt']
    )

    # Baseline scenario: Use neutral system prompt with original context/question
    baseline_user_prompt = f"Context: {example['original_context']}\n\nQuestion: {example['original_question']}"
    baseline_scenario = analyzer.apply_chat_template(
        system_prompt="You are a helpful assistant.",
        user_prompt=baseline_user_prompt
    )

    print("🎭 Scheming Scenario Prompt (first 200 chars):")
    print(scheming_scenario[:200] + "..." if len(scheming_scenario) > 200 else scheming_scenario)

    print("\n🎯 Baseline Scenario Prompt (first 200 chars):")
    print(baseline_scenario[:200] + "..." if len(baseline_scenario) > 200 else baseline_scenario)

    print(f"\n🎯 Expected Answers:")
    print(f"   Honest: {example.get('honest_answer', 'N/A')}")
    print(f"   Dishonest: {example.get('dishonest_answer', 'N/A')}")

    return scheming_scenario, baseline_scenario

# Create interactive widgets for example selection
example_slider = widgets.IntSlider(
    value=0,
    min=0,
    max=len(dataset) - 1,
    step=1,
    description='Example:',
    style={'description_width': 'initial'}
)

max_tokens_slider = widgets.IntSlider(
    value=50,
    min=10,
    max=100,
    step=5,
    description='Max tokens:',
    style={'description_width': 'initial'}
)

analyze_button = widgets.Button(
    description='🔍 Analyze Example',
    button_style='primary'
)

output_area = widgets.Output()

def on_analyze_click(button):
    with output_area:
        clear_output(wait=True)
        analyze_example(example_slider.value, max_tokens_slider.value)

analyze_button.on_click(on_analyze_click)

# Display interface
interface = widgets.VBox([
    widgets.HTML("<h3>🎛️ Example Analysis</h3>"),
    widgets.HBox([example_slider, max_tokens_slider]),
    analyze_button,
    output_area
])

display(interface)

# Show initial example
with output_area:
    analyze_example(0, 50)

print("🎉 Comprehensive Attention Analysis Notebook Ready!")
print("\n✅ Key Features Implemented:")
print("   • Complete model responses (not truncated at prompts)")
print("   • BertViz head view showing all attention heads")
print("   • Model view showing all layers always visible")
print("   • Per-token perplexity plots (user preference)")
print("   • Interactive controls for exploration")
print("   • Multi-GPU support with automatic detection")
print("   • Robust dataset validation and filtering")
print("   • Proper chat template application")
print("   • Clean output hierarchy with intuitive controls")
print("\n🚀 Use the interactive widgets above to explore attention patterns!")
print("\n📚 For full BertViz functionality, run the attention_analysis.py module")
print("   which contains complete implementations of all analysis functions.")